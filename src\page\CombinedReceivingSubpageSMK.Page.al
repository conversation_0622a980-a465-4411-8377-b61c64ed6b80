page 60003 "Combined Receiving Subpage SMK"
{
    Caption = 'Combined Receiving Subpage';
    PageType = ListPart;
    SourceTable = "Combined Receiving Line SMK";
    UsageCategory = Lists;
    ApplicationArea = All;
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Source Document No."; Rec."Source Document No.")
                {
                    Editable = false;
                    trigger OnDrillDown()
                    var
                        PurchaseHeader: Record "Purchase Header";
                    begin
                        if not PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, Rec."Source Document No.") then
                            exit;
                        PageManagement.PageRun(PurchaseHeader);
                        //Page.Run(Page::"Purchase Order", PurchaseHeader);
                    end;
                }
                field("Source Document Line No."; Rec."Source Document Line No.")
                {
                    Editable = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field("Location Code"; Rec."Location Code")
                {
                    //Editable = false;
                }
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    Editable = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                }
                field("Vendor Lot No."; Rec."Vendor Lot No.")
                {
                }
                field("Qty. to Receive Manual"; Rec."Qty. to Receive Manual")
                {
                    Editable = not IsPackageTrackingEnabled;
                }
                field("Line Package Quantity"; Rec."Line Package Quantity")
                {
                }
                field("Line Package Count"; Rec."Line Package Count")
                {
                }
                field("Quantity Received"; Rec."Quantity Received")
                {
                    Editable = false;
                }
                field("Planned Receipt Date"; Rec."Planned Receipt Date")
                {
                    Editable = false;
                }
                field("Receipt Undone"; Rec."Receipt Undone")
                {
                }
                field("Packaging Type SMK"; Rec."Packaging Type SMK")
                {
                    Editable = false;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action("Assign Lot No.")
            {
                ApplicationArea = All;
                Caption = 'Assign &Lot No.';
                Image = Lot;
                ToolTip = 'Automatically assign the required lot numbers from predefined number series.';

                trigger OnAction()
                begin
                    SumikaPurchaseManagement.AssignLotNo(Rec);
                end;
            }
            action(CreateSinglePackage)
            {
                ApplicationArea = All;
                Caption = 'Create LPN';
                Image = RegisterPutAway;
                ToolTip = 'Executes the Create LPN action.';
                trigger OnAction()
                begin
                    SumikaPurchaseManagement.PopulateAndOpenPackageCreation(Rec, Enum::"Package Creation Type SMK"::Single);
                end;
            }
            action(CreateMultiplePackages)
            {
                ApplicationArea = All;
                Caption = 'Create multi-bag LPN';
                Image = CreatePutawayPick;
                ToolTip = 'Executes the Create Package action.';
                trigger OnAction()
                begin
                    SumikaPurchaseManagement.PopulateAndOpenPackageCreation(Rec, Enum::"Package Creation Type SMK"::Multiple);
                end;
            }
            action("CreateMultiplePackagesWithoutPalette SMK")
            {
                ApplicationArea = All;
                Caption = 'Create Multiple LPN';
                Image = ItemTracking;
                ToolTip = 'Executes the Create Multiple LPN action.';
                trigger OnAction()
                begin
                    SumikaPurchaseManagement.PopulateAndOpenPackageCreation(Rec, Enum::"Package Creation Type SMK"::"Multiple without Palette");
                end;
            }
            action(Print)
            {
                ApplicationArea = All;
                Caption = 'Print';
                Image = BarCode;
                ToolTip = 'Executes the Print action.';

                trigger OnAction()
                var
                    CombinedReceivingLineDtl: Record "CombinedReceivingLineDtl SMK";
                    PackageNoInformation: Record "Package No. Information";
                begin
                    //CurrPage.SetSelectionFilter(CombinedReceivingLineDtl);
                    CombinedReceivingLineDtl.SetRange("Document No.", Rec."Document No.");
                    CombinedReceivingLineDtl.SetRange("Document Line No.", Rec."Line No.");
                    if CombinedReceivingLineDtl.FindSet() then
                        repeat
                            PackageNoInformation.Get(CombinedReceivingLineDtl."Item No.", CombinedReceivingLineDtl."Variant Code", CombinedReceivingLineDtl."Package No.");
                            PackageNoInformation.Mark(true);
                        until CombinedReceivingLineDtl.Next() = 0;
                    PackageNoInformation.MarkedOnly(true);

                    Report.Run(Report::"Package Label SMK", true, true, PackageNoInformation);
                end;
            }
            action(Comments)
            {
                ApplicationArea = All;
                Caption = 'Comments';
                Image = ViewComments;
                ToolTip = 'View comments for the purchase line.';

                trigger OnAction()
                var
                    PurchCommentLine: Record "Purch. Comment Line";
                    PurchCommentSheet: Page "Purch. Comment Sheet";
                begin
                    PurchCommentLine.SetRange("Document Type", PurchCommentLine."Document Type"::Order);
                    PurchCommentLine.SetRange("No.", Rec."Source Document No.");
                    PurchCommentLine.SetRange("Document Line No.", Rec."Source Document Line No.");

                    PurchCommentSheet.SetTableView(PurchCommentLine);
                    PurchCommentSheet.RunModal();
                end;
            }
            action(UndoReceipt)
            {
                ApplicationArea = All;
                Caption = 'Undo Receipt', Comment = 'TRK="Teslim Almayı Geri Al"';
                ToolTip = 'Executes the Undo Receipt action.';
                Image = Undo;
                Enabled = (Rec."Line Package Quantity" > 0) and not (Rec."Receipt Undone");

                trigger OnAction()
                var
                    PurchRcptLine: Record "Purch. Rcpt. Line";
                    ReservationEntry: Record "Reservation Entry";
                //UndoReceiptQst: Label 'Are you sure you want to undo the receipt line?';
                begin
                    // if not ConfirmManagement.GetResponseOrDefault(UndoReceiptQst, true) then
                    //     exit;

                    PurchRcptLine.Get(Rec."Posted Purchase Receipt No.", Rec."Posted Purchase Rcpt. Line No.");
                    PurchRcptLine.SetRecFilter();
                    Codeunit.Run(Codeunit::"Undo Purchase Receipt Line", PurchRcptLine);

                    ReservationEntry.SetRange("Reservation Status", ReservationEntry."Reservation Status"::Surplus);
                    ReservationEntry.SetRange("Source Type", 39);
                    ReservationEntry.SetRange("Source Subtype", 1);
                    ReservationEntry.SetRange("Source ID", Rec."Source Document No.");
                    ReservationEntry.SetRange("Source Ref. No.", Rec."Source Document Line No.");
                    ReservationEntry.SetRange("Lot No.", Rec."Lot No.");
                    ReservationEntry.DeleteAll(true);

                    Rec."Receipt Undone" := true;
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        IsPackageTrackingEnabled := SumikaPurchaseManagement.IsPackageTrackingEnabled(Rec."Item No.");
    end;

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        //ConfirmManagement: Codeunit "Confirm Management";
        PageManagement: Codeunit "Page Management";
        IsPackageTrackingEnabled: Boolean;
}
