table 60018 "Combined Shipment Line SMK"
{
    Caption = 'Combined Shipment Line';

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(3; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }
        field(4; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
            ToolTip = 'Specifies the value of the Source Document Line No. field.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(6; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            AllowInCustomizations = Always;
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies the value of the Quantity field.';

            trigger OnValidate()
            begin
                // Initialize Qty. to Ship when Quantity is set
                if Quantity <> xRec.Quantity then
                    Validate("Qty. to Ship", Quantity);
            end;
        }
        field(9; "Qty. to Ship"; Decimal)
        {
            Caption = 'Qty. to Ship';
            ToolTip = 'Specifies the value of the Qty. to Ship field.';

            trigger OnValidate()
            var
                Item: Record Item;
                PackagingType: Record "Packaging Type SMK";
                MoreQtyErr: Label 'Qty. to Ship cannot be greater than Quantity.';
                PackageTypeErr: Label 'Package splitting is not allowed for this packaging type.';
            begin
                if ("Qty. to Ship" > Quantity) then
                    Error(MoreQtyErr);

                // Only allow changes if packaging type allows splitting
                if "Qty. to Ship" <> Quantity then
                    if Item.Get("Item No.") then begin
                        PackagingType.Reset();
                        PackagingType.SetRange(Code, Item."Packaging Type SMK");
                        if PackagingType.FindFirst() and (not PackagingType."Allow Package Split") then
                            Error(PackageTypeErr);
                    end;
            end;

        }
        field(10; "Quantity Shipped"; Decimal)
        {
            Caption = 'Quantity Shipped';
            ToolTip = 'Specifies the value of the Quantity Shipped field.';
        }
        field(11; "Line Package Count"; Integer)
        {
            Caption = 'Line Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("CombinedShipmentLineDtl SMK" where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Line Package Count field.';
        }
        field(12; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(13; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(14; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
            ToolTip = 'Specifies the value of the Bin Code field.';
            trigger OnValidate()
            var
                SalesLine: Record "Sales Line";
            begin
                if xRec."Bin Code" = '' then
                    exit;

                if Rec."Bin Code" = xRec."Bin Code" then
                    exit;

                SalesLine.Get(SalesLine."Document Type"::Order, Rec."Source Document No.", Rec."Source Document Line No.");
                SalesLine.Validate("Bin Code", Rec."Bin Code");
                SalesLine.Modify(true);
            end;

        }
        field(15; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            AllowInCustomizations = Always;
        }
        field(16; "Line Package Quantity"; Decimal)
        {
            Caption = 'Line Package Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("CombinedShipmentLineDtl SMK".Quantity where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Line Package Quantity field.';
        }
        field(17; "Packaging Type"; Code[100])
        {
            Caption = 'Packaging Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("Item No.")));
            ToolTip = 'Specifies the packaging type of the item.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; "Source Document No.", "Source Document Line No.", "Item No.", "Variant Code")
        {
        }
    }
    trigger OnInsert()
    var
        CombinedShipmentLine: Record "Combined Shipment Line SMK";
    begin
        CombinedShipmentLine.SetRange("Document No.", Rec."Document No.");
        if CombinedShipmentLine.FindLast() then
            Rec."Line No." := CombinedShipmentLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}