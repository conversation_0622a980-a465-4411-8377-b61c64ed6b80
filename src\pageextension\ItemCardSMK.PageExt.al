pageextension 60016 "Item Card SMK" extends "Item Card"
{
    layout
    {
        addafter("No.")
        {
            field("No. 2 SMK"; Rec."No. 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the No. 2 field.';
            }
            field("Packaging Type SMK"; Rec."Packaging Type SMK")
            {
                ApplicationArea = All;
            }
        }
        addafter(Blocked)
        {
            field("Include In Mail SMK"; Rec."Include In Mail SMK")
            {
                ApplicationArea = All;
            }

        }
        addafter("Flushing Method")
        {
            field("Automatic Consumption SMK"; Rec."Automatic Consumption SMK")
            {
                ApplicationArea = All;
            }

        }
    }
    actions
    {
        addafter(CopyItem)
        {
            action("CopyItemSMK SMK")
            {
                ApplicationArea = All;
                Caption = 'Copy Item to Another Company';
                Promoted = true;
                PromotedCategory = Process;
                Image = CopyItem;
                ToolTip = 'Executes the Copy Item to Another Company action.';
                trigger OnAction()
                begin
                    DataSyncManagement.CopyItemToSelectedCompany(Rec);
                end;
            }
        }
    }
    var
        DataSyncManagement: Codeunit "Data Sync. Management SMK";
}