page 60001 "Combined Receiving LineDetail"
{
    Caption = 'Combined Receiving Line Detail';
    PageType = List;
    SourceTable = "CombinedReceivingLineDtl SMK";
    UsageCategory = Lists;
    //Editable = false;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Buy-From Vendor No."; Rec."Buy-From Vendor No.")
                {
                }
                field("Buy-from Vendor Name"; Rec."Buy-from Vendor Name")
                {
                }
                field("Purchase Order No."; Rec."Purchase Order No.")
                {
                }
                field("Purchase Order Line No."; Rec."Purchase Order Line No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Quantity-to Receive"; Rec."Qty. to Receive")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field(Received; Rec.Received)
                {
                }
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Vendor Lot No."; Rec."Vendor Lot No.")
                {
                }
                field("Parent Package No."; Rec."Parent Package No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }
    actions
    {
        area(Reporting)
        {
            action(Print)
            {
                ApplicationArea = All;
                Caption = 'Print';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                PromotedOnly = true;
                ToolTip = 'Executes the Print action.';

                trigger OnAction()
                var
                    CombinedReceivingLineDtl: Record "CombinedReceivingLineDtl SMK";
                    CombinedReceivingLineDtl2: Record "CombinedReceivingLineDtl SMK";
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(CombinedReceivingLineDtl);
                    if CombinedReceivingLineDtl.FindSet() then
                        repeat
                            //if (ProdOrderLineDetail.Quantity = 0) and (ProdOrderLineDetail."Parent Package No." = '') then begin
                            // if (ProdOrderLineDetail."Parent Package No." = '') then begin
                            CombinedReceivingLineDtl2.SetRange("Parent Package No.", CombinedReceivingLineDtl."Package No.");

                            PackageNoInformation.Get(CombinedReceivingLineDtl."Item No.", CombinedReceivingLineDtl."Variant Code", CombinedReceivingLineDtl."Package No.");
                            PackageNoInformation.Mark(true);
                            if CombinedReceivingLineDtl2.FindSet() then //begin
                                repeat
                                    PackageNoInformation.Get(CombinedReceivingLineDtl2."Item No.", CombinedReceivingLineDtl2."Variant Code", CombinedReceivingLineDtl2."Package No.");
                                    PackageNoInformation.Mark(true);
                                until CombinedReceivingLineDtl2.Next() = 0;
                        // end
                        // end
                        // else begin
                        //     PackageNoInformation.Get(ProdOrderLineDetail."Item No.", ProdOrderLineDetail."Variant Code", ProdOrderLineDetail."Package No.");
                        //     PackageNoInformation.Mark(true);
                        // end;
                        until CombinedReceivingLineDtl.Next() = 0;

                    PackageNoInformation.MarkedOnly(true);

                    //Message('%1', PackageNoInformation.Count);

                    Report.Run(Report::"Package Label SMK", true, true, PackageNoInformation);
                end;
            }
        }
    }
}
