table 60008 "Quality Control Header SMK"
{
    DataClassification = CustomerContent;
    Caption = 'Quality Control Header';
    DrillDownPageId = "Quality Control SMK";
    LookupPageId = "Quality Controls SMK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                SumikaSetup: Record "Sumika Setup SMK";
                NoSeriesManagement: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    SumikaSetup.Get();
                    NoSeriesManagement.TestManual(SumikaSetup."Quality Control No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; Type; Enum "Quality Control Type SMK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                "Item Description" := SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode("Item No.", "Variant Code");

                if Item.Get("Item No.") then
                    Validate("Item Description 2", Item."Description 2")
                else
                    Validate("Item Description 2", '');
            end;
        }
        field(4; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
            trigger OnValidate()
            begin
                "Item Description" := SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode("Item No.", "Variant Code");
            end;
        }
        field(5; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(6; Status; Enum "Quality Control Status SMK")
        {
            Caption = 'Status';
            InitValue = "Input Pending";
            ToolTip = 'Specifies the value of the Status field.';
        }
        field(7; "Responsible User ID"; Code[50])
        {
            Caption = 'Responsible User ID';
            TableRelation = "User Setup"."User ID";
            ToolTip = 'Specifies the value of the Responsible User ID field.';
        }
        field(8; "Quality Employee User ID"; Code[50])
        {
            Caption = 'Quality Employee User ID';
            TableRelation = "User Setup"."User ID";
            ToolTip = 'Specifies the value of the Quality Employee User ID field.';
        }
        field(9; "Date"; Date)
        {
            Caption = 'Date';
            ToolTip = 'Specifies the value of the Date field.';
        }
        field(10; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            TableRelation = "Lot No. Information"."Lot No." where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"));
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(11; "Item Description 2"; Text[50])
        {
            Caption = 'Item Description 2';
            ToolTip = 'Specifies the value of the Item Description 2 field.';
        }
        field(12; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            ToolTip = 'Specifies the value of the Customer No. field.';
        }
        field(13; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Customer.Name where("No." = field("Customer No.")));
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
        field(14; "Shipment No."; Code[20])
        {
            Caption = 'Shipment No.';
            ToolTip = 'Specifies the value of the Shipment No. field.';
        }
        field(15; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(16; "Production Order No."; Code[20])
        {
            Caption = 'Production Order No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Production Order No. SMK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(17; "Package Order No."; Integer)
        {
            Caption = 'Package Order No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Package Order No. SMK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Package Order No. field.';
        }
        field(18; "QC Control Quantity"; Decimal)
        {
            Caption = 'Q.C. Control Quantity';
            ToolTip = 'Specifies the value of the Q.C. Control Quantity field.';
        }
        field(19; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
        }


        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(Key2; "Lot No.", Posted, "Package No.")
        {
        }
    }

    trigger OnInsert()
    var
        SumikaSetup: Record "Sumika Setup SMK";
        NoSeriesManagement: Codeunit "No. Series";

    begin
        // if "No." = '' then begin
        //     SumikaSetup.Get();
        //     SumikaSetup.TestField("Quality Control No. Series");
        //     NoSeriesManagement.InitSeries(SumikaSetup."Quality Control No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            SumikaSetup.Get();
            SumikaSetup.TestField("Quality Control No. Series");
            "No. Series" := SumikaSetup."Quality Control No. Series";
            if NoSeriesManagement.AreRelated(SumikaSetup."Quality Control No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

    end;

    trigger OnDelete()
    var
        QualityControlLine: Record "Quality Control Line SMK";
        DeletionNotAllowedErr: Label 'Deletion is not allowed for posted Quality Control documents.';
    begin
        if Posted then
            Error(DeletionNotAllowedErr);

        QualityControlLine.SetRange("Document No.", Rec."No.");
        QualityControlLine.DeleteAll(true);
    end;

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
}