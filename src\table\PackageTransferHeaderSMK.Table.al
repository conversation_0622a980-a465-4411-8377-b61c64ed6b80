table 60013 "Package Transfer Header SMK"
{
    DataClassification = CustomerContent;
    Caption = 'Package Transfer Header';
    DrillDownPageId = "Package Transfer Orders SMK";
    LookupPageId = "Package Transfer Orders SMK";
    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                SumikaSetup: Record "Sumika Setup SMK";
                NoSeriesManagement: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    SumikaSetup.Get();
                    NoSeriesManagement.TestManual(SumikaSetup."Package Transfer No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        // field(2; "Transfer-from Code"; Code[10])
        // {
        //     Caption = 'Transfer-from Code';
        //     TableRelation = Location where("Use As In-Transit" = const(false));
        // }
        // field(3; "Transfer-from Bin Code"; Code[20])
        // {
        //     Caption = 'Transfer-from Bin Code';
        //     TableRelation = Bin.Code where("Location Code" = field("Transfer-from Code"));
        // }
        field(4; "Transfer-to Code"; Code[10])
        {
            Caption = 'Transfer-to Code';
            TableRelation = Location where("Use As In-Transit" = const(false));
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
        }
        field(5; "Transfer-To Bin Code"; Code[20])
        {
            Caption = 'Transfer-To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Transfer-to Code"));
            ToolTip = 'Specifies the value of the Transfer-To Bin Code field.';
        }
        field(6; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(7; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            begin
                SumikaPackageTransMgt.ProcessBarcode(Rec);
            end;
        }
        field(8; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
        }
        field(9; "Production Order No."; Code[20])
        {
            Caption = 'Production Order No.';
            TableRelation = "Production Order"."No." where(Status = const(Released));
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(10; "Transferring to Prod. Location"; Boolean)
        {
            Caption = 'Transferring to Prod. Location';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Production Location SMK" where(Code = field("Transfer-to Code")));
            ToolTip = 'Specifies the value of the Transferring to Prod. Location field.';
        }
        field(11; "Total Transfer Quantity"; Decimal)
        {
            Caption = 'Total Transfer Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Package Transfer Line SMK".Quantity where("Document No." = field("No.")));
            ToolTip = 'Specifies the value of the Total Transfer Quantity field.';
        }

        field(12; "Location Filter"; Code[250])
        {
            Caption = 'Location Filter';
            ToolTip = 'Specifies the value of the Location Filter field.';
        }
        field(2; "Created from Shipment"; Boolean)
        {
            Caption = 'Created from Shipment';
            ToolTip = 'Specifies the value of the Created from Shipment field.';
            AllowInCustomizations = Always;
        }


        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        SumikaSetup: Record "Sumika Setup SMK";
        UserSetup: Record "User Setup";
        NoSeriesManagement: Codeunit "No. Series";
    begin
        // if "No." = '' then begin
        //     SumikaSetup.Get();
        //     SumikaSetup.TestField("Package Transfer No. Series");
        //     NoSeriesManagement.InitSeries(SumikaSetup."Package Transfer No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            SumikaSetup.Get();
            SumikaSetup.TestField("Package Transfer No. Series");
            "No. Series" := SumikaSetup."Package Transfer No. Series";
            if NoSeriesManagement.AreRelated(SumikaSetup."Package Transfer No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

        "Posting Date" := WorkDate();

        UserSetup.Get(UserId());

        Rec."Location Filter" := UserSetup."Location Filter SMK";
    end;

    trigger OnDelete()
    begin
        Rec.TestField(Posted, false);
    end;

    var
        SumikaPackageTransMgt: Codeunit "Sumika Package Trans. Mgt. SMK";
}