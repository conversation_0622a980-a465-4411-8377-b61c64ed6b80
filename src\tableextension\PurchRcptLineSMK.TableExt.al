tableextension 60002 "Purch. Rcpt. Line SMK" extends "Purch. Rcpt. Line"
{
    fields
    {
        field(60000; "Buy-from Vendor Name SMK"; Text[100])
        {
            Caption = 'Buy-from Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Rcpt. Header"."Buy-from Vendor Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Buy-from Vendor Name field.';
        }
        field(60001; "Vendor Shipment No. SMK"; Code[35])
        {
            Caption = 'Vendor Shipment No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purch. Rcpt. Header"."Vendor Shipment No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Vendor Shipment No. field.';
        }
        field(60002; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("No.")));
            ToolTip = 'Specifies the packaging type of the item.';
        }
    }
}