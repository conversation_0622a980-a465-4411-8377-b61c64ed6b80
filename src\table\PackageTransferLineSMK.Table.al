table 60014 "Package Transfer Line SMK"
{
    Caption = 'Package Transfer Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(3; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(4; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(5; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(6; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(7; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(8; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(9; "Transfer-from Code"; Code[10])
        {
            Editable = false;
            Caption = 'Transfer-from Code';
            TableRelation = Location where("Use As In-Transit" = const(false));
            ToolTip = 'Specifies the value of the Transfer-from Code field.';
        }
        field(10; "Transfer-from Bin Code"; Code[20])
        {
            Editable = false;
            Caption = 'Transfer-from Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Transfer-from Code"));
            ToolTip = 'Specifies the value of the Transfer-from Bin Code field.';
        }
        field(11; "Transfer-to Code"; Code[10])
        {
            Editable = false;
            Caption = 'Transfer-to Code';
            TableRelation = Location where("Use As In-Transit" = const(false));
            ToolTip = 'Specifies the value of the Transfer-to Code field.';
        }
        field(12; "Transfer-To Bin Code"; Code[20])
        {
            Caption = 'Transfer-To Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Transfer-to Code"));
            ToolTip = 'Specifies the value of the Transfer-To Bin Code field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; "Document No.", "Package No.")
        {
            Unique = true;
        }
    }
    trigger OnInsert()
    var
        PackageTransferLine: Record "Package Transfer Line SMK";
    begin
        PackageTransferLine.SetRange("Document No.", Rec."Document No.");
        if PackageTransferLine.FindLast() then
            Rec."Line No." := PackageTransferLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}