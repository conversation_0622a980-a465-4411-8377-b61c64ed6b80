tableextension 60013 "Purch. Comment Line SMK" extends "Purch. Comment Line"
{
    fields
    {
        // field(60000; "Created By SMK"; Code[50])
        // {
        //     Caption = 'Created By';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup(User."User Name" where("User Security ID" = field(SystemCreatedBy)));
        //     ToolTip = 'Specifies the value of the Created By field.';
        // }
        field(60001; "Comment Added By SMK"; Code[50])
        {
            Caption = 'Comment Added By';
            ToolTip = 'Specifies the value of the Comment Added By field.';
        }
    }
    trigger OnBeforeInsert()
    begin
        if "Comment Added By SMK" = '' then
            "Comment Added By SMK" := CopyStr(UserId(), 1, MaxStrLen("Comment Added By SMK"));

        if Rec.Date = 0D then
            Rec.Date := WorkDate();
    end;
}