page 60019 "Package Transfer Orders SMK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Orders';
    PageType = List;
    SourceTable = "Package Transfer Header SMK";
    UsageCategory = Documents;
    Editable = false;
    CardPageId = "Package Transfer Order SMK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                }
                field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Production Order No."; Rec."Production Order No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(Posted; Rec.Posted)
                {
                    Editable = false;
                }
            }
        }
    }
}