page 60013 "Quality Control SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control Document';
    PageType = Document;
    SourceTable = "Quality Control Header SMK";
    UsageCategory = None;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Item Description 2"; Rec."Item Description 2")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Responsible User ID"; Rec."Responsible User ID")
                {
                }
                field("Quality Employee User ID"; Rec."Quality Employee User ID")
                {
                }
                field("Date"; Rec.Date)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Shipment No."; Rec."Shipment No.")
                {
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        ItemLedgerEntry: Record "Item Ledger Entry";
                        SalesShipmentHeader: Record "Sales Shipment Header";
                    begin
                        ItemLedgerEntry.SetRange("Item No.", Rec."Item No.");
                        ItemLedgerEntry.SetRange("Variant Code", Rec."Variant Code");
                        ItemLedgerEntry.SetRange("Entry Type", ItemLedgerEntry."Entry Type"::Sale);
                        ItemLedgerEntry.SetRange("Lot No.", Rec."Lot No.");
                        ItemLedgerEntry.SetRange("Source No.", Rec."Customer No.");
                        if ItemLedgerEntry.FindSet(false) then
                            repeat
                                SalesShipmentHeader.Get(ItemLedgerEntry."Document No.");
                                SalesShipmentHeader.Mark(true);
                            until ItemLedgerEntry.Next() = 0;
                        SalesShipmentHeader.MarkedOnly(true);

                        if Page.RunModal(Page::"Posted Sales Shipments", SalesShipmentHeader) = Action::LookupOK then
                            Rec."Shipment No." := SalesShipmentHeader."No.";
                    end;
                }

                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
            part(Lines; "Quality Control Subpage SMK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PopulateLines)
            {
                ApplicationArea = All;
                Caption = 'Populate Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = LinesFromJob;
                ToolTip = 'Executes the Populate Lines action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    SumikaQualityCtrlMgt.PopulateQualityControlLines(Rec);
                end;
            }
        }
        area(Reporting)
        {
            action(QualityControl)
            {
                ApplicationArea = All;
                Caption = 'Print Quality Control';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PrintDocument;
                ToolTip = 'Executes the Print Quality Control action.';
                PromotedOnly = true;
                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Quality Control SMK", true, true, Rec);
                end;
            }
        }
    }
    var
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
}