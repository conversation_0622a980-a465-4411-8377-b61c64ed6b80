report 60001 "Production Order Report SMK"
{
    ApplicationArea = All;
    Caption = 'Production Order Report';
    UsageCategory = ReportsAndAnalysis;
    RDLCLayout = 'ProductionOrderReport.rdl';

    dataset
    {
        dataitem(ProductionOrder; "Production Order")
        {
            column(No; "No.")
            {
            }
            column(MachineScaleNoSMK_ProductionOrder; "Machine Scale No. SMK")
            {
            }
            column(PackagingType_ProductionOrder; "Packaging Type SMK")
            {
            }
            column(Description; Description)
            {
            }
            column(SourceNo; "Source No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(StartingDate; "Starting Date")
            {
            }
            column(EndingDate; "Ending Date")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(BarrelTempCSMK; "Barrel Temp. (°C) SMK")
            {
            }
            column(ScrewSpeedrpmSMK; "Screw Speed (rpm) SMK")
            {
            }
            column(TorqueSMK; "Torque (%) SMK")
            {
            }
            column(PelletizationSpeedm_min_SMK; "Pellet. Speed (m/min) SMK")
            {
            }
            column(PelletTemperature__C_SMK; "Pellet Temperature (°C) SMK")
            {
            }
            column(VaccuumbarSMK; "Vaccuum (bar) SMK")
            {
            }
            column(OutputkgsaatSMK; "Output (kg/saat) SMK")
            {
            }
            column(BathTemperatureCSMK; "Bath Temperature (°C) SMK")
            {
            }
            column(FilterDiameterMeshSMK; "Filter Diameter (Mesh) SMK")
            {
            }
            column(MachineNoSMK; "Machine No. SMK")
            {
            }
            column(EncodedText; EncodedText)
            {
            }
            dataitem("Prod. Order Component"; "Prod. Order Component")
            {
                DataItemLink = "Prod. Order No." = field("No.");
                DataItemTableView = where("Prod. Order Line No." = const(10000));


                column(LineNo_ProdOrderComponent; "Line No.")
                {
                }
                column(ItemNo_ProdOrderComponent; "Item No.")
                {
                }
                column(VariantCode_ProdOrderComponent; "Variant Code")
                {
                }
                column(Description_ProdOrderComponent; Description)
                {
                }
                column(Quantityper_ProdOrderComponent; "Quantity per")
                {
                }
                column(QtyperUnitofMeasure_ProdOrderComponent; "Qty. per Unit of Measure")
                {
                }
                column(ExpectedQuantity_ProdOrderComponent; "Expected Quantity")
                {
                }
                column(FeederNoSMK_ProdOrderComponent; "Feeder No. SMK")
                {
                }
                dataitem("Reservation Entry"; "Reservation Entry")
                {
                    DataItemLink = "Source Prod. Order Line" = field("Prod. Order Line No."), "Source Ref. No." = field("Line No."), "Source ID" = field("Prod. Order No.");

                    column(LotNo_ReservationEntry; "Lot No.")
                    {
                    }
                }
            }
            dataitem("Temperature Parameter SMK"; "Temperature Parameter SMK")
            {
                DataItemLink = "Item No." = field("Source No."), "Machine No." = field("Machine No. SMK");

                column(BarrelNo_TemperatureParameterSMK; "Barrel No.")
                {
                }
                column(TemperatureC_TemperatureParameterSMK; "Temperature (°C)")
                {
                }
            }
            dataitem("Prod. Order Line"; "Prod. Order Line")
            {
                DataItemLink = Status = field(Status), "Prod. Order No." = field("No.");
                column(LotNoSMK_ProdOrderLine; "Lot No. SMK")
                {
                }
                column(Description2; SumikaProductionMgt.GetDescription2FromItemNo("Prod. Order Line"."Item No."))
                {
                }
            }
            dataitem("Company Information"; "Company Information")
            {
                column(Name_CompanyInformation; Name)
                {
                }
            }

            trigger OnAfterGetRecord()
            var
                BarcodeSymbology: Enum "Barcode Symbology";
                IBarcodeFontProvider: Interface "Barcode Font Provider";

            // BarcodeSymbology2D: Enum "Barcode Symbology 2D";
            // IBarcodeFontProvider2D: Interface "Barcode Font Provider 2D";
            begin
                IBarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeSymbology := Enum::"Barcode Symbology"::Code128;
                IBarcodeFontProvider.ValidateInput("No.", BarcodeSymbology);
                EncodedText := IBarcodeFontProvider.EncodeFont("No.", BarcodeSymbology);

                // BarcodeSymbology2D := Enum::"Barcode Symbology 2D"::"QR-Code";
                // IBarcodeFontProvider2D := Enum::"Barcode Font Provider 2D"::IDAutomation2D;
                // EncodedText := IBarcodeFontProvider2D.EncodeFont("No.", BarcodeSymbology2D);

            end;

        }
    }
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
        EncodedText: Text;
}