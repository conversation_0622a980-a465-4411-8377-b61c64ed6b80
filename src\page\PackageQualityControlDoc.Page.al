page 60038 "Package Quality Control Doc."
{
    ApplicationArea = All;
    Caption = 'Package Quality Control Doc.';
    PageType = Document;
    SourceTable = "Quality Control Header SMK";
    UsageCategory = None;
    InsertAllowed = false;
    DeleteAllowed = false;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = not Rec.Posted;
                // field("No."; Rec."No.")
                // {
                // }
                field("Package No."; Rec."Package No.")
                {
                }
                field("QC Control Quantity"; Rec."QC Control Quantity")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                }
                field("Production Order No."; Rec."Production Order No.")
                {
                    Editable = false;
                }

                field("Package Order No."; Rec."Package Order No.")
                {
                    Editable = false;
                }

                field(Posted; Rec.Posted)
                {
                    Editable = false;
                }
            }
            part(Lines; "Q.C. Line Dtl. Subpage SMK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No."), "Package No." = field("Package No.");
                Editable = not Rec.Posted;
            }

        }
    }
    actions
    {
        area(Processing)
        {
            action(Post)
            {
                ApplicationArea = All;
                Caption = 'Post', Comment = 'TRK="Deftere Naklet"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Post;
                ToolTip = 'Post the Quality Control Document.';

                trigger OnAction()
                begin
                    SumikaQualityCtrlMgt.PostQualityControlDocument(Rec);
                end;
            }

            action(PackageComment)
            {
                ApplicationArea = All;
                Caption = 'Package Comment', Comment = 'TRK="Paket Yorumu"';
                Image = Comment;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'View or add comments for the package.';
                RunObject = page "Item Tracking Comments";
                RunPageLink = "Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Serial/Lot No." = field("Package No.");
            }
        }
    }
    var
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
    // trigger OnClosePage()
    // var
    //     QualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
    // begin
    //     QualityControlLineDtl.SetRange("Document No.", Rec."No.");
    //     QualityControlLineDtl.SetRange("Package No.", Rec."Package No.");
    //     if QualityControlLineDtl.FindSet() then
    //         repeat
    //             if (QualityControlLineDtl."Result Value" = 0) and (QualityControlLineDtl."Selection Value" = QualityControlLineDtl."Selection Value"::" ") then
    //                 QualityControlLineDtl.Delete(true);
    //         until QualityControlLineDtl.Next() = 0;
    // end;
}