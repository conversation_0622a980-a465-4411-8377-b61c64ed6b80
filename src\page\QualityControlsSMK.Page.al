page 60015 "Quality Controls SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control Documents';
    PageType = List;
    SourceTable = "Quality Control Header SMK";
    SourceTableView = where(Posted = const(false), "Package No." = filter(''));
    UsageCategory = Lists;
    CardPageId = "Quality Control SMK";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Item Description 2"; Rec."Item Description 2")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Responsible User ID"; Rec."Responsible User ID")
                {
                }
                field("Quality Employee User ID"; Rec."Quality Employee User ID")
                {
                }
                field("Date"; Rec.Date)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}