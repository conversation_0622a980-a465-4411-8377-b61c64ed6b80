page 60015 "Quality Controls SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control Documents';
    PageType = List;
    SourceTable = "Quality Control Header SMK";
    SourceTableView = where(Posted = const(false), "Package No." = filter(''));
    UsageCategory = Lists;
    CardPageId = "Quality Control SMK";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Item Description 2"; Rec."Item Description 2")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Remaining Quantity"; CalculateRemainingQuantityForLot(Rec."Item No.", Rec."Variant Code", Rec."Lot No."))
                {
                    ApplicationArea = All;
                    Caption = 'Remaining Quantity';
                    ToolTip = 'Specifies the remaining quantity for this lot from Item Ledger Entries.';

                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.SetRange("Item No.", Rec."Item No.");
                        PackageNoInformation.SetRange("Variant Code", Rec."Variant Code");
                        PackageNoInformation.SetRange("Lot No. SMK", Rec."Lot No.");
                        Page.RunModal(Page::"Package No. Information List", PackageNoInformation);
                    end;
                }
                field("Responsible User ID"; Rec."Responsible User ID")
                {
                }
                field("Quality Employee User ID"; Rec."Quality Employee User ID")
                {
                }
                field("Date"; Rec.Date)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }

    local procedure CalculateRemainingQuantityForLot(ItemNo: Code[20]; VariantCode: Code[10]; LotNo: Code[50]): Decimal
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        TotalRemainingQty: Decimal;
    begin
        if LotNo = '' then
            exit(0);

        ItemLedgerEntry.SetLoadFields("Remaining Quantity");
        ItemLedgerEntry.SetRange("Item No.", ItemNo);
        ItemLedgerEntry.SetRange("Variant Code", VariantCode);
        ItemLedgerEntry.SetRange("Lot No.", LotNo);
        ItemLedgerEntry.SetRange(Open, true);

        if ItemLedgerEntry.FindSet() then
            repeat
                TotalRemainingQty += ItemLedgerEntry."Remaining Quantity";
            until ItemLedgerEntry.Next() = 0;

        exit(TotalRemainingQty);
    end;
}