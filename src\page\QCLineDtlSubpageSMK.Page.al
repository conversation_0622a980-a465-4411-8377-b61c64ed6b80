page 60039 "Q.C. Line Dtl. Subpage SMK"
{
    ApplicationArea = All;
    Caption = 'Q.C. Line Dtl. Subpage SMK';
    PageType = ListPart;
    SourceTable = "Quality Control Line Dtl. SMK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Specification Description"; Rec."Specification Description")
                {
                    Editable = false;
                    StyleExpr = LineStyle;
                }
                field("Result Value"; Rec."Result Value")
                {
                    StyleExpr = LineStyle;

                    trigger OnValidate()
                    begin
                        SetLineStyle();
                    end;
                }
                field("Selection Result Value"; Rec."Selection Result Value")
                {
                    StyleExpr = LineStyle;

                    trigger OnValidate()
                    begin
                        SetLineStyle();
                    end;
                }
                field("Specification Reference"; Rec."Specification Reference")
                {
                    Editable = false;
                    StyleExpr = LineStyle;
                }
                field("Min Value"; Rec."Min Value")
                {
                    Editable = false;
                    StyleExpr = LineStyle;
                }
                field("Max Value"; Rec."Max Value")
                {
                    Editable = false;
                    StyleExpr = LineStyle;
                }
                field("Exact Value"; Rec."Exact Value")
                {
                    Editable = false;
                    StyleExpr = LineStyle;
                }
            }
        }
    }

    trigger OnAfterGetRecord()
    begin
        SetLineStyle();
    end;

    local procedure SetLineStyle()
    var
        IsUnfavorable: Boolean;
    begin
        LineStyle := Format(PageStyle::Standard);
        IsUnfavorable := false;

        if Rec."Specification Reference" in [Rec."Specification Reference"::Between,
                                            Rec."Specification Reference"::Equal,
                                            Rec."Specification Reference"::Greater,
                                            Rec."Specification Reference"::Less] then
            case Rec."Specification Reference" of
                Rec."Specification Reference"::Between:
                    if not ((Rec."Result Value" >= Rec."Min Value") and (Rec."Result Value" <= Rec."Max Value")) then
                        IsUnfavorable := true;
                Rec."Specification Reference"::Equal:
                    if Rec."Result Value" <> Rec."Exact Value" then
                        IsUnfavorable := true;
                Rec."Specification Reference"::Greater:
                    if Rec."Result Value" < Rec."Min Value" then
                        IsUnfavorable := true;
                Rec."Specification Reference"::Less:
                    if Rec."Result Value" > Rec."Max Value" then
                        IsUnfavorable := true;
            end;

        if Rec."Specification Reference" = Rec."Specification Reference"::Selection then
            if Rec."Selection Result Value" <> Rec."Selection Value" then
                IsUnfavorable := true;

        if IsUnfavorable then
            LineStyle := Format(PageStyle::Unfavorable);
    end;

    var
        LineStyle: Text;
}