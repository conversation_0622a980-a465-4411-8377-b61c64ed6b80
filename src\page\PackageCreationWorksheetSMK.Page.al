page 60005 "Package Creation Worksheet SMK"
{
    ApplicationArea = All;
    Caption = 'Package Creation Worksheet';
    PageType = Card;
    SourceTable = "Package Creation SMK";
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field(Type; Rec."Type")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                }
                field("Parent Package No."; Rec."Parent Package No.")
                {
                }

            }
            group(SinglePackage)
            {
                Caption = 'Single Package';
                Visible = (Rec.Type = Rec.Type::Single);

                field("Package Quantity"; Rec."Package Quantity")
                {
                    ShowMandatory = true;
                    BlankZero = true;
                }
                field("Qty. to Receive"; Rec."Qty. to Receive")
                {
                    Editable = false;
                }
                field("Number of Packages"; Rec."Number of Packages")
                {
                    Editable = false;
                }
            }
            group(MultiplePackage)
            {
                Caption = 'Multiple Package';
                Visible = (Rec.Type <> Rec.Type::Single);

                field("Package Quantity Multiple";
                Rec."Package Quantity")
                {
                    ShowMandatory = true;
                    BlankZero = true;
                }
                field("Qty. to Receive Multiple"; Rec."Qty. to Receive")
                {
                    ShowMandatory = true;
                    BlankZero = true;
                }
                field("Number of Packages Multiple"; Rec."Number of Packages")
                {
                    ShowMandatory = true;
                    BlankZero = true;
                    Editable = false;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CreatePackages)
            {
                ApplicationArea = All;
                Caption = 'Create Packages';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = CreateWarehousePick;
                ToolTip = 'Executes the Create Packages action.';
                trigger OnAction()
                begin
                    SumikaPurchaseManagement.CreatePackagesFromPackageCreation(Rec);
                    CurrPage.Close();
                end;
            }
            // action(AssignParentPackageNo)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Assign Parent Package No.';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = NewLotProperties;
            //     ToolTip = 'Executes the Assign Parent Package No. action.';

            //     trigger OnAction()
            //     begin
            //         if Rec."Entry Type" = Rec."Entry Type"::Purchase then
            //             case Rec."Entry Type" of
            //                 Rec."Entry Type"::Purchase:
            //                     SumikaProductionMgt.AssignParentPackageNo(Rec);
            //                     ;
            //                 Rec."Entry Type"::Production:
            //                     ;
            //             end;

            //     end;
            // }
        }
    }
    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
    //SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}