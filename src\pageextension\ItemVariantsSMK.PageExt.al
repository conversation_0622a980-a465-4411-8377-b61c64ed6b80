pageextension 60036 "Item Variants SMK" extends "Item Variants"
{
    actions
    {
        addfirst(Processing)
        {
            action("CopyItemVariantToAnotherCompany SMK")
            {
                ApplicationArea = All;
                Caption = 'Copy Item Variant to Another Company';
                Promoted = true;
                PromotedCategory = Process;
                Image = CopyToTask;
                ToolTip = 'Executes the Copy Item Variant to Another Company action.';
                trigger OnAction()
                var
                    DataSyncManagement: Codeunit "Data Sync. Management SMK";
                begin
                    DataSyncManagement.CopyItemVariantsToSelectedCompany(Rec);
                end;
            }
        }
    }
}