tableextension 60006 "Production Order SMK" extends "Production Order"
{
    fields
    {
        field(60000; "Barrel Temp. (°C) SMK"; Integer)
        {
            Caption = 'Barrel Temperature (°C)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Temperature Parameter SMK" where("Machine No." = field("Machine No. SMK"), "Item No." = field("Source No.")));
            ToolTip = 'Specifies the value of the Barrel Temperature (°C) field.';
        }
        field(60001; "Screw Speed (rpm) SMK"; Text[50])
        {
            Caption = 'Screw Speed (rpm)';
            ToolTip = 'Specifies the value of the Screw Speed (rpm) field.';
        }
        field(60002; "Torque (%) SMK"; Text[50])
        {
            Caption = 'Torque (%)';
            ToolTip = 'Specifies the value of the Torque (%) field.';
        }
        field(60003; "Vaccuum (bar) SMK"; Text[50])
        {
            Caption = 'Vaccuum (bar)';
            ToolTip = 'Specifies the value of the Vaccuum (bar) field.';
        }
        field(60004; "Output (kg/saat) SMK"; Text[50])
        {
            Caption = 'Output (kg/saat)';
            ToolTip = 'Specifies the value of the Output (kg/saat) field.';
        }
        field(60005; "Bath Temperature (°C) SMK"; Text[50])
        {
            Caption = 'Bath Temperature (°C)';
            ToolTip = 'Specifies the value of the Bath Temperature field.';
        }
        field(60006; "Filter Diameter (Mesh) SMK"; Text[50])
        {
            Caption = 'Filter Diameter (Mesh)';
            ToolTip = 'Specifies the value of the Filter Diameter (Mesh) field.';
        }
        field(60007; "Machine No. SMK"; Code[20])
        {
            Caption = 'Work Center/Machine No.';
            TableRelation = if ("Type SMK" = const("Machine Center")) "Machine Center"
            else if ("Type SMK" = const("Work Center")) "Work Center";
            ToolTip = 'Specifies the value of the Machine No. field.';
            trigger OnValidate()
            begin
                SumikaProductionMgt.PopulateProductionParameters(Rec);
            end;
        }
        field(60008; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            TableRelation = "Packaging Type SMK".Code;
            Editable = false;
            ToolTip = 'Specifies the value of the Packaging Type field.';
        }
        field(60009; "Machine Scale No. SMK"; Code[10])
        {
            Caption = 'Machine Scale No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Machine Scale No. field.';
        }
        field(60010; "Type SMK"; Enum "Capacity Type Journal")
        {
            Caption = 'Type';
            ValuesAllowed = 0, 1;
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(60024; "Pellet. Speed (m/min) SMK"; Decimal)
        {
            Caption = 'Pelletization Speed (m/min)';
            ToolTip = 'Specifies the value of the Pelletization Speed (m/min) field.';
            DataClassification = CustomerContent;
            DecimalPlaces = 0 : 2;
        }
        field(60025; "Pellet Temperature (°C) SMK"; Decimal)
        {
            Caption = 'Pellet Temperature (°C)';
            ToolTip = 'Specifies the value of the Pellet Temperature (°C) field.';
            DataClassification = CustomerContent;
            DecimalPlaces = 0 : 2;
        }
    }
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}