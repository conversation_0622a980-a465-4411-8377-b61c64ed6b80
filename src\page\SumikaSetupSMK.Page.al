page 60004 "Sumika Setup SMK"
{
    PageType = Card;
    SourceTable = "Sumika Setup SMK";
    Caption = 'Sumika Setup';
    InsertAllowed = false;
    DeleteAllowed = false;
    UsageCategory = Administration;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            group(PurchaseSetup)
            {
                Caption = 'Purchase Setup';
                field("Combined Receiving No. Series"; Rec."Combined Receiving No. Series")
                {
                }
                // field("Barcode Transfer No. Series"; Rec."Barcode Transfer No. Series")
                // {
                //     ToolTip = 'Specifies the value of the Barcode Transfer No. Series field.';
                // }
            }
            group(QualityControlSetup)
            {
                Caption = 'Quality Control Setup';
                field("Quality Control No. Series"; Rec."Quality Control No. Series")
                {
                }
                field("Temp. Quality Control Nos"; Rec."Temp. Quality Control Nos")
                {
                }
            }
            group(PackageTransferSetup)
            {
                Caption = 'Package Transfer Setup';

                field("Package Transfer No. Series"; Rec."Package Transfer No. Series")
                {
                }
                field("Package Transfer Template Name"; Rec."Package Transfer Template Name")
                {
                }
                field("Package Transfer Batch Name"; Rec."Package Transfer Batch Name")
                {
                }
                field("Parent Package No. Series"; Rec."Parent Package No. Series")
                {
                }
            }
            group(PackageSplitSetup)
            {
                Caption = 'Package Split Setup';

                field("Package Split No. Series"; Rec."Package Split No. Series")
                {
                }
                field("Package Split Jnl. Tmpl. Name"; Rec."Package Split Jnl. Tmpl. Name")
                {
                }
                field("Package Split Jnl. Batch Name"; Rec."Package Split Jnl. Batch Name")
                {
                }
            }
            group(ProductionSetup)
            {
                Caption = 'Production Setup';
                field("Output Journal Template Name"; Rec."Output Journal Template Name")
                {
                }
                field("Output Journal Batch Name"; Rec."Output Journal Batch Name")
                {
                }
                field("Consumption Jnl. Template Name"; Rec."Consumption Jnl. Template Name")
                {
                }
                field("Consumption Jnl. Batch Name"; Rec."Consumption Jnl. Batch Name")
                {
                }
                field("Print Label Quantity"; Rec."Print Label Quantity")
                {
                }
            }
            group(SalesSetup)
            {
                Caption = 'Sales Setup';
                field("Combined Shipment No. Series"; Rec."Combined Shipment No. Series")
                {
                }
                field("Transfer&Ship Comb. Shipment"; Rec."Transfer&Ship Comb. Shipment")
                {
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        Rec.InsertIfNotExists();
    end;
}
