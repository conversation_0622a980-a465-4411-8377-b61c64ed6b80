table 60017 "Dimension - G/L Acc. Setup SMK"
{
    Caption = 'Dimension - G/L Account Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Dimension - G/L Acc. Setup SMK";
    LookupPageId = "Dimension - G/L Acc. Setup SMK";

    fields
    {
        field(1; "Dimension 1 Code"; Code[20])
        {
            Caption = 'Dimension 1 Code';
            TableRelation = Dimension;
            ToolTip = 'Specifies the value of the Dimension 1 Code field.';
        }
        field(2; "Dimension 1 Value Code"; Code[20])
        {
            Caption = 'Dimension 1 Value Code';
            TableRelation = "Dimension Value".Code where("Dimension Code" = field("Dimension 1 Code"));
            ToolTip = 'Specifies the value of the Dimension 1 Code Value field.';
        }
        field(3; "Dimension 2 Code"; Code[20])
        {
            Caption = 'Dimension 2 Code';
            TableRelation = Dimension;
            ToolTip = 'Specifies the value of the Dimension 2 Code field.';
        }
        field(4; "Dimension 2 Value Code"; Code[20])
        {
            Caption = 'Dimension 1 Value Code';
            TableRelation = "Dimension Value".Code where("Dimension Code" = field("Dimension 2 Code"));
            ToolTip = 'Specifies the value of the Dimension 2 Code Value field.';
        }
        field(5; "G/L Account No."; Code[20])
        {
            Caption = 'G/L Account No.';
            TableRelation = "G/L Account";
            ToolTip = 'Specifies the value of the G/L Account No. field.';
        }
        field(6; "Dimension 1 Value Name"; Text[50])
        {
            Caption = 'Dimension 1 Value Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Dimension Value".Name where("Dimension Code" = field("Dimension 1 Code"), Code = field("Dimension 1 Value Code")));
            ToolTip = 'Specifies the value of the Dimension 1 Value Name field.';
        }
        field(7; "Dimension 2 Value Name"; Text[50])
        {
            Caption = 'Dimension 2 Value Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Dimension Value".Name where("Dimension Code" = field("Dimension 2 Code"), Code = field("Dimension 2 Value Code")));
            ToolTip = 'Specifies the value of the Dimension 2 Value Name field.';
        }

    }
    keys
    {
        key(PK; "Dimension 1 Code", "Dimension 1 Value Code", "Dimension 2 Code", "Dimension 2 Value Code")
        {
            Clustered = true;
        }
    }
}