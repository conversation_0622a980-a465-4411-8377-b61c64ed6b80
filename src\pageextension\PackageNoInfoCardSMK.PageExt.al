pageextension 60009 "Package No. Info. Card SMK" extends "Package No. Information Card"
{
    layout
    {
        addafter(Description)
        {

            field("Description 2 SMK"; Rec."Description 2 SMK")
            {
                ApplicationArea = All;
            }
        }
        addlast(General)
        {
            field("Vendor Lot No. SMK"; Rec."Vendor Lot No. SMK")
            {
                ApplicationArea = All;
            }
            field("Inventory Entry Date SMK"; Rec."Inventory Entry Date SMK")
            {
                ApplicationArea = All;
            }
            field("Certificate Number SMK"; Rec."Certificate Number")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Certificate Number field.';
            }
            field("Expiration Date SMK"; SumikaBasicFunctions.GetExpirationDateFromLotNo(Rec."Lot No. SMK"))
            {
                Caption = 'Expiration Date';
                ToolTip = 'Specifies the value of the Expiration Date field.';
                ApplicationArea = All;
            }
            field("Inventory SMK"; Rec.Inventory)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the quantity on inventory with this line.';
            }
            field("Label Quantity SMK"; Rec."Label Quantity SMK")
            {
                ApplicationArea = All;
            }
            field("Lot No. SMK"; Rec."Lot No. SMK")
            {
                ApplicationArea = All;
            }
            field("Package Order No. SMK"; Rec."Package Order No. SMK")
            {
                ApplicationArea = All;
            }
            field("Shelf Life SMK"; Rec."Shelf Life SMK")
            {
                ApplicationArea = All;
            }
            field("Production Order No. SMK"; Rec."Production Order No. SMK")
            {
                ApplicationArea = All;
            }
            field("Parent Package No. SMK"; Rec."Parent Package No. SMK")
            {
                ApplicationArea = All;
            }
            field("Old Package No. SMK"; Rec."Old Package No. SMK")
            {
                ApplicationArea = All;
            }
            field("Location Code SMK"; Rec."Location Code SMK")
            {
                ApplicationArea = All;
            }
            field("Bin Code SMK"; SumikaPackageTransMgt.GetBinCodeFromPackageNoInformation(Rec))
            {
                Caption = 'Bin Code';
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Bin Code field.';
            }
            field("Quality Control Status SMK"; Rec."Quality Control Status SMK")
            {
                ApplicationArea = All;
            }
            // field("Parent Package Inventory SMK"; Rec."Parent Package Inventory SMK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Parent Package Inventory field.';
            // }
        }
    }
    actions
    {
        addfirst("&Package")
        {
            action("PackageLabel SMK")
            {
                ApplicationArea = All;
                Caption = 'Print Package Label';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print Package Label action.';

                trigger OnAction()
                begin
                    Rec.SetRecFilter();
                    Report.Run(Report::"Package Label SMK", true, true, Rec);
                end;
            }

            action("Comment SMK")
            {
                Caption = 'Comment';
                Image = ViewComments;
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                RunObject = page "Item Tracking Comments";
                RunPageLink = "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Serial/Lot No." = field("Package No.");
                ToolTip = 'View or add comments for the record.';
            }
        }
        // modify(Comment)
        // {
        //     ApplicationArea = All;
        // }
    }
    var
        SumikaPackageTransMgt: Codeunit "Sumika Package Trans. Mgt. SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
}