pageextension 60010 "Lot No. Information Card SMK" extends "Lot No. Information Card"
{
    layout
    {
        addlast(General)
        {
            field("Vendor Lot No. SMK"; Rec."Vendor Lot No. SMK")
            {
                ApplicationArea = All;
            }
            field("Company No. SMK"; Rec."Company No. SMK")
            {
                ApplicationArea = All;
            }
            field("Company Name SMK"; Rec."Company Name SMK")
            {
                ApplicationArea = All;
            }
            field("Expiration Date SMK"; SumikaBasicFunctions.GetExpirationDateFromLotNo(Rec."Lot No."))
            {
                Caption = 'Expiration Date';
                ToolTip = 'Specifies the value of the Expiration Date field.';
                ApplicationArea = All;
            }
            field("Comment SMK"; Rec.Comment)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies that a comment has been recorded for the lot number.';
            }
        }
    }
    var
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
}