pageextension 60048 "Sales Order SMK" extends "Sales Order"
{
    layout
    {
        addlast(General)
        {

            field("Completed SMK"; Rec."Completed SMK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addafter("Co&mments")
        {
            action("Test Posted Sales Shipment SMK")
            {
                ApplicationArea = All;
                Caption = 'Test Posted Sales Shipment', Comment = 'TRK="YourLanguageCaption"';
                Image = TestFile;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Executes the Test Posted Sales Shipment action.';
                Visible = false;
                trigger OnAction()
                var
                    BusinessEventHandler: Codeunit "Business Event Handler SMK";
                begin
                    BusinessEventHandler.OnAfterPostSalesHeaderBe(Rec."Last Shipping No.", Rec."No.", Rec."Location Code", Rec."Sell-to Customer No.", Rec."Sell-to Customer Name", Rec."External Document No.");
                end;
            }
        }
    }
}