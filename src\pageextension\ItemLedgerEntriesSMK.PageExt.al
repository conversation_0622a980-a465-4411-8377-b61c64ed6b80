pageextension 60031 "Item Ledger Entries SMK" extends "Item Ledger Entries"
{
    layout
    {
        addafter(Description)
        {
            field("Source Type SMK"; Rec."Source Type")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the source type that applies to the source number, shown in the Source No. field.';
            }
            field("Source No. SMK"; Rec."Source No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies where the entry originated.';
            }
            field("Source Name SMK"; SumikaSalesManagement.GetSourceNameFromILE(Rec))
            {
                ApplicationArea = All;
                Caption = 'Source Name';
                ToolTip = 'Specifies where the entry originated.';
            }
            field("Vendor Lot No. SMK"; Rec."Vendor Lot No. SMK")
            {
                ApplicationArea = All;
            }
        }
        addafter("Cost Amount (Actual)")
        {
            field("Cost Amt. (Actual) by Date SMK"; Rec."Cost Amt. (Actual) by Date SMK")
            {
                ApplicationArea = All;
            }
        }
    }
    var
        SumikaSalesManagement: Codeunit "Sumika Sales Management SMK";
}