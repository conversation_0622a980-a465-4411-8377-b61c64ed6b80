page 60018 "Package Transfer Subpage SMK"
{
    ApplicationArea = All;
    Caption = 'Lines';
    PageType = ListPart;
    SourceTable = "Package Transfer Line SMK";
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field(Description; Rec.Description)
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Transfer-from Code"; Rec."Transfer-from Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Transfer-from Bin Code"; Rec."Transfer-from Bin Code")
                {
                    Editable = false;
                    QuickEntry = false;
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    QuickEntry = false;
                }
                field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                {
                    QuickEntry = false;
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action("Comment SMK")
            {
                Caption = 'Comment';
                Image = ViewComments;
                ApplicationArea = All;
                RunObject = page "Item Tracking Comments";
                RunPageLink = "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Serial/Lot No." = field("Package No.");
                ToolTip = 'View or add comments for the record.';
            }
        }
    }
}