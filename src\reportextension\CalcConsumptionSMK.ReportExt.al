reportextension 60000 "Calc. Consumption SMK" extends "Calc. Consumption"
{
    requestpage
    {
        layout
        {
            addafter(CalcBasedOn)
            {
                field("Use Manual Output SMK"; UseManualOutput)
                {
                    ApplicationArea = All;
                    Caption = 'Use Manual Output';
                    ToolTip = 'Specifies whether to use manual output quantity for consumption calculation.';

                    trigger OnValidate()
                    begin
                        ManualOutputQuantityEnabled := UseManualOutput;
                    end;
                }
                field("Manual Output Quantity SMK"; ManualOutputQuantity)
                {
                    ApplicationArea = All;
                    Caption = 'Manual Output Quantity';
                    ToolTip = 'Specifies the manual output quantity to use for consumption calculation.';
                    Enabled = ManualOutputQuantityEnabled;
                    DecimalPlaces = 0 : 5;

                    trigger OnValidate()
                    begin
                        if ManualOutputQuantity < 0 then
                            Error(NegativeQuantityErr);
                    end;
                }
            }
        }
    }

    trigger OnPreReport()
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
    begin
        if UseManualOutput then begin
            if ManualOutputQuantity <= 0 then
                Error(ZeroQuantityErr);
            SumikaProductionMgt.SetManualOutputParameters(true, ManualOutputQuantity);
        end else
            SumikaProductionMgt.SetManualOutputParameters(false, 0);
    end;

    trigger OnPostReport()
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
    begin
        // Reset manual output parameters after report execution
        SumikaProductionMgt.SetManualOutputParameters(false, 0);
    end;

    var
        UseManualOutput: Boolean;
        ManualOutputQuantity: Decimal;
        ManualOutputQuantityEnabled: Boolean;
        NegativeQuantityErr: Label 'Manual Output Quantity cannot be negative.';
        ZeroQuantityErr: Label 'Manual Output Quantity must be greater than zero when Use Manual Output is enabled.';
}
