report 60000 "Package Label SMK"
{
    ApplicationArea = All;
    Caption = 'Package Label';
    UsageCategory = ReportsAndAnalysis;
    RDLCLayout = 'PackageLabel.rdl';
    dataset
    {
        dataitem(PackageNoInformation; "Package No. Information")
        {
            CalcFields = Inventory;
            column(ItemNo; "Item No.")
            {
            }
            column(Description2; "Description 2 SMK")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(PackageNo; "Package No.")
            {
            }
            column(MachineScaleNo; SumikaProductionMgt.GetMachineScaleNoFromPackageNoInformation(PackageNoInformation))
            {
            }
            column(Description; Description)
            {
            }
            column(LotNoSMK; "Lot No. SMK")
            {
            }
            column(VendorLotNoSMK; "Vendor Lot No. SMK")
            {
            }
            column(ExpirationDateSMK; "Expiration Date SMK")
            {
            }
            column(ApprovalDateSMK; "Inventory Entry Date SMK")
            {
            }
            column(She<PERSON>LifeSMK; "Shelf Life SMK")
            {
            }
            column(UnitOfMeasureCode; Item."Base Unit of Measure")
            {
            }
            column(Inventory; VarInventory)
            {
            }
            column(BarCode; EncodedText)
            {
            }
            column(ParentPackageNoSMK_PackageNoInformation; "Parent Package No. SMK")
            {
            }
            column(Package_Order_No__SMK; "Package Order No. SMK")
            {
            }
            trigger OnAfterGetRecord()
            var
                BarcodeSymbology: Enum "Barcode Symbology";
                IBarcodeFontProvider: Interface "Barcode Font Provider";
            begin
                IBarcodeFontProvider := Enum::"Barcode Font Provider"::IDAutomation1D;
                BarcodeSymbology := Enum::"Barcode Symbology"::Code128;
                IBarcodeFontProvider.ValidateInput("Package No.", BarcodeSymbology);
                EncodedText := IBarcodeFontProvider.EncodeFont("Package No.", BarcodeSymbology);

                SumikaSetup.Get();
                CalcFields(Inventory);
                if SumikaSetup."Print Label Quantity" then
                    VarInventory := "Label Quantity SMK"
                else
                    VarInventory := Inventory;

                // Get Item record to retrieve the Base Unit of Measure
                if Item.Get("Item No.") then;

            end;

        }
    }
    var
        SumikaSetup: Record "Sumika Setup SMK";
        Item: Record Item;
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
        EncodedText: Text;
        VarInventory: Decimal;
}