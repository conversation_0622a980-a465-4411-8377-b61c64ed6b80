tableextension 60003 "Purchase Header SMK" extends "Purchase Header"
{
    fields
    {
        field(60000; "Combined Receiving No. SMK"; Code[20])
        {
            Caption = 'Combined Receiving No.';
            AllowInCustomizations = Always;
        }
        field(60001; "Created By SMK"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(User."User Name" where("User Security ID" = field(SystemCreatedBy)));
            ToolTip = 'Specifies the value of the Created By field.';
        }
        field(60002; "Completed SMK"; Boolean)
        {
            Caption = 'Completed';
            ToolTip = 'Specifies the value of the Completed field.';
            trigger OnValidate()
            begin
                SumikaPurchaseManagement.OnAfterValidateCompletedOnPurchaseHeader(Rec);
            end;
        }

    }
    keys
    {
        key(SK; "Combined Receiving No. SMK")
        {
        }
    }
    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
}