pageextension 60033 "Purchase Return Order SMK" extends "Purchase Return Order"
{
    actions
    {
        addfirst("F&unctions")
        {
            action("NewGLAccountLine SMK")
            {
                ApplicationArea = All;
                Caption = 'New G/L Account Line';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = GL;
                ToolTip = 'Executes the New G/L Account Line action.';

                trigger OnAction()
                var
                    NewGLAccountLine: Page "New G/L Account Line SMK";
                begin
                    NewGLAccountLine.GetPurchaseHeader(Rec);
                    NewGLAccountLine.RunModal();
                end;
            }
        }
    }
}