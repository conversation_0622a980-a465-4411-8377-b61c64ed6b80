codeunit 60003 "Sumika Package Trans. Mgt. SMK"
{
    procedure ProcessBarcode(var PackageTransferHeader: Record "Package Transfer Header SMK")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageTransferHeader.Barcode = '' then
            exit;

        PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.SetRange("Package No.", PackageTransferHeader.Barcode);
        PackageNoInformation.FindFirst();
        if PackageNoInformation.Inventory = 0 then begin
            PackageNoInformation.Reset();
            PackageNoInformation.SetRange("Parent Package No. SMK", PackageTransferHeader.Barcode);
            if PackageNoInformation.FindSet(false) then
                repeat
                    CreatePackageTransferLine(PackageTransferHeader, PackageNoInformation);
                until PackageNoInformation.Next() = 0
        end
        else begin
            PackageNoInformation.Reset();
            PackageNoInformation.SetRange("Package No.", PackageTransferHeader.Barcode);
            PackageNoInformation.FindFirst();

            CreatePackageTransferLine(PackageTransferHeader, PackageNoInformation);
        end;

        PackageTransferHeader.Barcode := '';
    end;

    procedure OnAfterAction_Post_PagePackageTransferOrder(PackageTransferHeader: Record "Package Transfer Header SMK")
    var
        IsHandled: Boolean;
    begin
        PackageTransferHeader.TestField(Posted, false);

        IsHandled := false;
        OnBeforePost_PagePackageTransferOrder(PackageTransferHeader, IsHandled);
        if IsHandled then
            exit;

        CreateItemReclassRecords(PackageTransferHeader);
    end;

    [IntegrationEvent(false, false)]
    local procedure OnBeforePost_PagePackageTransferOrder(var PackageTransferHeader: Record "Package Transfer Header SMK"; var IsHandled: Boolean)
    begin
    end;

    procedure CreateItemReclassRecords(PackageTransferHeader: Record "Package Transfer Header SMK")
    var
        ItemJournalLine: Record "Item Journal Line";
        PackageTransferLine: Record "Package Transfer Line SMK";
        PackageNoInformation: Record "Package No. Information";
        ItemJournalBatch: Record "Item Journal Batch";
        Item: Record Item;
        NoSeriesManagement: Codeunit "No. Series";
        DocumentNo: Code[20];
        NoLinesFoundErr: Label 'No Package Transfer Lines found for this document.';
        ItemJournalLineNo: Integer;
    begin
        PackageTransferHeader.CalcFields("Transferring to Prod. Location");
        if PackageTransferHeader."Transferring to Prod. Location" and not PackageTransferHeader."Created from Shipment" then
            PackageTransferHeader.TestField("Production Order No.");

        PackageTransferLine.SetRange("Document No.", PackageTransferHeader."No.");
        if not PackageTransferLine.FindSet() then
            Error(NoLinesFoundErr);

        SumikaSetup.GetRecordOnce();
        SumikaSetup.TestField("Package Transfer Template Name");
        SumikaSetup.TestField("Package Transfer Batch Name");

        ItemJournalBatch.Get(SumikaSetup."Package Transfer Template Name", SumikaSetup."Package Transfer Batch Name");
        if ItemJournalBatch."No. Series" <> '' then begin
            Clear(NoSeriesManagement);
            //DocumentNo := NoSeriesManagement.TryGetNextNo(ItemJournalBatch."No. Series", WorkDate());
            DocumentNo := NoSeriesManagement.PeekNextNo(ItemJournalBatch."No. Series")
        end;

        ItemJournalLine.SetRange("Journal Template Name", SumikaSetup."Package Transfer Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", SumikaSetup."Package Transfer Batch Name");
        if ItemJournalLine.FindLast() then
            ItemJournalLineNo := ItemJournalLine."Line No." + 10000
        else
            ItemJournalLineNo := 10000;

        repeat
            Clear(ItemJournalLine);
            ItemJournalLine.Init();
            ItemJournalLine."Journal Template Name" := SumikaSetup."Package Transfer Template Name";
            ItemJournalLine."Journal Batch Name" := SumikaSetup."Package Transfer Batch Name";
            ItemJournalLine."Line No." := ItemJournalLineNo;
            //ItemJournalLine.SetUpNewLine(ItemJournalLine);
            ItemJournalLine.Insert(true);
            //Commit();

            ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Transfer);
            ItemJournalLine."Posting Date" := PackageTransferHeader."Posting Date";
            ItemJournalLine."Document Date" := PackageTransferHeader."Posting Date";
            ItemJournalLine."Document No." := DocumentNo;
            ItemJournalLine."Posting No. Series" := ItemJournalBatch."Posting No. Series";
            ItemJournalLine.Validate("Item No.", PackageTransferLine."Item No.");
            ItemJournalLine.Validate("Variant Code", PackageTransferLine."Variant Code");
            ItemJournalLine.Validate("Location Code", PackageTransferLine."Transfer-from Code");
            ItemJournalLine.Validate("Bin Code", PackageTransferLine."Transfer-from Bin Code");
            ItemJournalLine.Validate("New Location Code", PackageTransferLine."Transfer-to Code");
            ItemJournalLine.Validate("New Bin Code", PackageTransferLine."Transfer-To Bin Code");
            ItemJournalLine.Validate(Quantity, PackageTransferLine.Quantity);

            ItemJournalLine.Validate("Lot No.", PackageTransferLine."Lot No.");
            ItemJournalLine.Validate("Package No.", PackageTransferLine."Package No.");

            ItemJournalLine.Modify(true);

            AssignLotAndPackageNoToItemJournalLine(ItemJournalLine, PackageTransferLine."Lot No.", PackageTransferLine."Package No.");
            ItemJournalLineNo += 10000;

            PackageNoInformation.Get(PackageTransferLine."Item No.", PackageTransferLine."Variant Code", PackageTransferLine."Package No.");

            Item.Get(PackageNoInformation."Item No.");
            if Item."Replenishment System" <> Item."Replenishment System"::"Prod. Order" then
                PackageNoInformation.Validate("Production Order No. SMK", PackageTransferHeader."Production Order No.");

            PackageNoInformation.Modify(true);
        until PackageTransferLine.Next() = 0;

        Codeunit.Run(Codeunit::"Item Jnl.-Post Batch", ItemJournalLine); //Post item journal line

        //Codeunit.Run(Codeunit::"Item Jnl.-Post", ItemJournalLine);


        PackageTransferHeader.Validate(Posted, true);
        PackageTransferHeader.Modify(true);
    end;

    local procedure AssignLotAndPackageNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; LotNo: Code[50]; PackageNo: Code[50])
    var
        TempReservEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservStatus: Enum "Reservation Status";
    begin
        TempReservEntry.Init();
        TempReservEntry."Entry No." := 1;
        TempReservEntry."Lot No." := LotNo; //use Serial No. for SN
        TempReservEntry."Package No." := PackageNo;
        TempReservEntry.Quantity := ItemJournalLine.Quantity;

        // TempReservEntry."New Lot No." := LotNo;
        // TempReservEntry."New Package No." := PackageNo;

        //TempReservEntry."Expiration Date" := Today();
        TempReservEntry.Insert(false);

        //CreateReservEntry.SetDates(0D, TempReservEntry."Expiration Date");

        if ItemJournalLine."Entry Type" = ItemJournalLine."Entry Type"::Transfer then //movement
            CreateReservEntry.SetNewTrackingFromItemJnlLine(ItemJournalLine);

        ItemJournalLine."Lot No." := '';
        ItemJournalLine."Package No." := '';
        ItemJournalLine.Modify(true);


        CreateReservEntry.CreateReservEntryFor(
          Database::"Item Journal Line", ItemJournalLine."Entry Type".AsInteger(),
          ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name", 0, ItemJournalLine."Line No.", ItemJournalLine."Qty. per Unit of Measure",
          TempReservEntry.Quantity, TempReservEntry.Quantity * ItemJournalLine."Qty. per Unit of Measure", TempReservEntry);

        CreateReservEntry.CreateEntry(
          ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Location Code", '', 0D, 0D, 0, ReservStatus::Surplus);
    end;

    local procedure CreatePackageTransferLine(var PackageTransferHeader: Record "Package Transfer Header SMK"; var PackageNoInformation: Record "Package No. Information")
    var
        PackageTransferLine: Record "Package Transfer Line SMK";
        Location: Record Location;
    begin
        PackageNoInformation.CalcFields(Inventory, "Location Code SMK");

        QualityControlStatusCheckOnTransferingProducationLocation(PackageTransferHeader, PackageNoInformation);


        PackageTransferLine.Init();
        PackageTransferLine."Document No." := PackageTransferHeader."No.";
        PackageTransferLine.Insert(true);
        PackageTransferLine.Validate("Package No.", PackageNoInformation."Package No.");
        PackageTransferLine.Validate("Item No.", PackageNoInformation."Item No.");
        PackageTransferLine.Validate("Variant Code", PackageNoInformation."Variant Code");
        PackageTransferLine.Validate(Description, PackageNoInformation.Description);
        PackageTransferLine.Validate(Quantity, PackageNoInformation.Inventory);
        PackageTransferLine.Validate("Lot No.", PackageNoInformation."Lot No. SMK");
        PackageTransferLine.Validate("Transfer-from Code", PackageNoInformation."Location Code SMK");
        PackageTransferLine.Validate("Transfer-from Bin Code", GetBinCodeFromPackageNoInformation(PackageNoInformation));
        PackageTransferLine.Validate("Transfer-to Code", PackageTransferHeader."Transfer-to Code");
        PackageTransferLine.Validate("Transfer-To Bin Code", PackageTransferHeader."Transfer-To Bin Code");
        PackageTransferLine.Modify(true);

        Location.Get(PackageTransferLine."Transfer-from Code");
        Location.TestField("Block Package Trans. Order SMK", false);

        Location.Get(PackageTransferLine."Transfer-to Code");
        Location.TestField("Block Package Trans. Order SMK", false);
    end;

    local procedure QualityControlStatusCheckOnTransferingProducationLocation(var PackageTransferHeader: Record "Package Transfer Header SMK"; var PackageNoInformation: Record "Package No. Information")
    var
        LotNoInformation: Record "Lot No. Information";
        QualityControlHeader: Record "Quality Control Header SMK";
        QCPendingErr: Label 'Quality Control is pending for Lot No.: %1.', Comment = '%1 is Lot No.';
    begin
        PackageTransferHeader.CalcFields("Transferring to Prod. Location");

        if PackageTransferHeader."Transferring to Prod. Location" then
            if LotNoInformation.Get(PackageNoInformation."Item No.", PackageNoInformation."Variant Code", PackageNoInformation."Lot No. SMK") then
                if QualityControlHeader.Get(LotNoInformation."Certificate Number") then
                    if QualityControlHeader.Status = QualityControlHeader.Status::"Input Pending" then
                        Error(QCPendingErr, LotNoInformation."Lot No.");
    end;

    procedure GetBinCodeFromPackageNoInformation(PackageNoInformation: Record "Package No. Information"): Code[20]
    var
        WarehouseEntry: Record "Warehouse Entry";
    begin
        PackageNoInformation.CalcFields("Location Code SMK");

        WarehouseEntry.SetRange("Item No.", PackageNoInformation."Item No.");
        WarehouseEntry.SetRange("Variant Code", PackageNoInformation."Variant Code");
        WarehouseEntry.SetRange("Package No.", PackageNoInformation."Package No.");
        WarehouseEntry.SetRange("Location Code", PackageNoInformation."Location Code SMK");
        WarehouseEntry.SetFilter(Quantity, '>0');
        if WarehouseEntry.FindLast() then
            exit(WarehouseEntry."Bin Code");

        exit('');
    end;

    var
        SumikaSetup: Record "Sumika Setup SMK";
}