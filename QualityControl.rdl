﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="No">
          <DataField>No</DataField>
        </Field>
        <Field Name="ItemNo">
          <DataField>ItemNo</DataField>
        </Field>
        <Field Name="VariantCode">
          <DataField>VariantCode</DataField>
        </Field>
        <Field Name="ItemDescription">
          <DataField>ItemDescription</DataField>
        </Field>
        <Field Name="Status">
          <DataField>Status</DataField>
        </Field>
        <Field Name="LotNo">
          <DataField>LotNo</DataField>
        </Field>
        <Field Name="SystemCreatedAt">
          <DataField>SystemCreatedAt</DataField>
        </Field>
        <Field Name="ResponsibleUserID">
          <DataField>ResponsibleUserID</DataField>
        </Field>
        <Field Name="QualityEmployeeUserID">
          <DataField>QualityEmployeeUserID</DataField>
        </Field>
        <Field Name="SpecificationCode_QualityControlLineSMK">
          <DataField>SpecificationCode_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="SpecificationDescription_QualityControlLineSMK">
          <DataField>SpecificationDescription_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="Standard_QualityControlLineSMK">
          <DataField>Standard_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="AverageResultValue_QualityControlLineSMK">
          <DataField>AverageResultValue_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="UnitofMeasureCode_QualityControlLineSMK">
          <DataField>UnitofMeasureCode_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="SelectionValue_QualityControlLineSMK">
          <DataField>SelectionValue_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="ExactValue_QualityControlLineSMK">
          <DataField>ExactValue_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="ExactValue_QualityControlLineSMKFormat">
          <DataField>ExactValue_QualityControlLineSMKFormat</DataField>
        </Field>
        <Field Name="MaxValue_QualityControlLineSMK">
          <DataField>MaxValue_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="MaxValue_QualityControlLineSMKFormat">
          <DataField>MaxValue_QualityControlLineSMKFormat</DataField>
        </Field>
        <Field Name="MinValue_QualityControlLineSMK">
          <DataField>MinValue_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="MinValue_QualityControlLineSMKFormat">
          <DataField>MinValue_QualityControlLineSMKFormat</DataField>
        </Field>
        <Field Name="SpecificationReference_QualityControlLineSMK">
          <DataField>SpecificationReference_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="LineStatus_QualityControlLineSMK">
          <DataField>LineStatus_QualityControlLineSMK</DataField>
        </Field>
        <Field Name="Name_CompanyInformation">
          <DataField>Name_CompanyInformation</DataField>
        </Field>
        <Field Name="Address_CompanyInformation">
          <DataField>Address_CompanyInformation</DataField>
        </Field>
        <Field Name="Address2_CompanyInformation">
          <DataField>Address2_CompanyInformation</DataField>
        </Field>
        <Field Name="City_CompanyInformation">
          <DataField>City_CompanyInformation</DataField>
        </Field>
        <Field Name="County_CompanyInformation">
          <DataField>County_CompanyInformation</DataField>
        </Field>
        <Field Name="PostCode_CompanyInformation">
          <DataField>PostCode_CompanyInformation</DataField>
        </Field>
        <Field Name="CountryRegionCode_CompanyInformation">
          <DataField>CountryRegionCode_CompanyInformation</DataField>
        </Field>
        <Field Name="PhoneNo_CompanyInformation">
          <DataField>PhoneNo_CompanyInformation</DataField>
        </Field>
        <Field Name="Name_Customer">
          <DataField>Name_Customer</DataField>
        </Field>
        <Field Name="Address_Customer">
          <DataField>Address_Customer</DataField>
        </Field>
        <Field Name="Address2_Customer">
          <DataField>Address2_Customer</DataField>
        </Field>
        <Field Name="City_Customer">
          <DataField>City_Customer</DataField>
        </Field>
        <Field Name="County_Customer">
          <DataField>County_Customer</DataField>
        </Field>
        <Field Name="PostCode_Customer">
          <DataField>PostCode_Customer</DataField>
        </Field>
        <Field Name="CountryRegionCode_Customer">
          <DataField>CountryRegionCode_Customer</DataField>
        </Field>
        <Field Name="No_SalesShipmentHeader">
          <DataField>No_SalesShipmentHeader</DataField>
        </Field>
        <Field Name="OrderNo_SalesShipmentHeader">
          <DataField>OrderNo_SalesShipmentHeader</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>