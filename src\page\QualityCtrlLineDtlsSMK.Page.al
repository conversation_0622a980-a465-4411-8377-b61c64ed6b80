page 60014 "Quality Ctrl. Line Dtls. SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control Line Details';
    PageType = List;
    SourceTable = "Quality Control Line Dtl. SMK";
    UsageCategory = ReportsAndAnalysis;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Document No."; Rec."Document No.")
                {
                }
                field("Document Line No."; Rec."Document Line No.")
                {
                }
                field("Line No."; Rec."Line No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                    DrillDown = true;

                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        if PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then
                            Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }
                field(Description; Rec.Description)
                {
                    DrillDown = false;
                }
                field("Package Order No."; Rec."Package Order No.")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Line Status"; Rec."Line Status")
                {
                }
                field("Specification Code"; Rec."Specification Code")
                {
                }
                field("Specification Description"; Rec."Specification Description")
                {
                }
                field("Specification Reference"; Rec."Specification Reference")
                {
                }
                field("Min Value"; Rec."Min Value")
                {
                }
                field("Max Value"; Rec."Max Value")
                {
                }
                field("Exact Value"; Rec."Exact Value")
                {
                }
                field("Selection Value"; Rec."Selection Value")
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field(Standard; Rec.Standard)
                {
                }
                field("Result Value"; Rec."Result Value")
                {
                }
                field("Selection Result Value"; Rec."Selection Result Value")
                {
                }
                field("QC Control Quantity"; Rec."QC Control Quantity")
                {
                }
                field("Source Q.C. Document No."; Rec."Source Q.C. Document No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(PackageComment)
            {
                ApplicationArea = All;
                Caption = 'Package Comment', Comment = 'TRK="Paket Yorumu"';
                Image = Comment;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ToolTip = 'View or add comments for the package.';
                RunObject = page "Item Tracking Comments";
                RunPageLink = "Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Serial/Lot No." = field("Package No.");
            }
        }
    }
}