page 60002 "Combined Receiving List SMK"
{
    ApplicationArea = All;
    Caption = 'Combined Receiving List';
    PageType = List;
    SourceTable = "Combined Receiving Header SMK";
    UsageCategory = Lists;
    CardPageId = "Combined Receiving SMK";
    SourceTableView = sorting("No.") order(descending);
    Editable = false;
    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                }
                field(Status; Rec.Status)
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Vendor Shipment No."; Rec."Vendor Shipment No.")
                {
                }
                field("Created By"; Rec."Created By")
                {
                }
                field("Created At"; Rec."Created At")
                {
                }
                // field("Total Received Package Count"; Rec."Total Received Package Count")
                // {
                //     ToolTip = 'Specifies the value of the Total Received Package Count field.';
                // }
                field("Total Received Quantity"; Rec."Total Package Quantity")
                {
                }
            }
        }
    }
}
