# Sumika Polymer Compounds - Business Central AL Extension

## Project Overview
This Business Central AL extension provides manufacturing and quality control solutions for Sumika Polymer Compounds, implementing specialized workflows for chemical manufacturing, packaging, and shipment processes.

## Architecture & Key Concepts

### Core Domain: Package-Centric Operations
- **Package No. Information**: Central entity for tracking production packages throughout their lifecycle
- **Lot No. Information**: Groups packages by production lots with quality certificates
- **Quality Control**: Multi-stage testing workflow (Purchase → Production → Shipment)
- **Combined Receiving/Shipment**: Batch processing of multiple orders/shipments

### Data Flow Pattern
```
Purchase → Combined Receiving → Package Creation → Quality Control → Production → Package Transfer → Combined Shipment
```

### Critical Business Rules
1. **Package Tracking**: All items require package-specific tracking via "Package No. Information"
2. **Quality Gates**: Items must pass quality control before advancing to next stage
3. **Package Splitting**: Some packaging types allow splitting for partial shipments
4. **Combined Processing**: Multiple purchase orders/sales orders processed together

## Naming Conventions

### Object Naming
- All custom objects suffixed with "SMK"
- Object ID range: 60000-60999
- Examples: `"Quality Control Header SMK"`, `SumikaQualityCtrlMgtSMK`

### Field Naming
- Custom fields: Field name + " SMK" suffix
- Examples: `"Feeder No. SMK"`, `"Combined Receiving No. SMK"`

### Codeunit Structure
- Management codeunits handle complex business logic: `SumikaQualityCtrlMgtSMK`, `SumikaPurchaseManagementSMK`
- Copy codeunits for data duplication: `CopyItemQltyCtrlSpecSMK`
- Basic functions in: `SumikaBasicFunctionsSMK`

## Key Workflows

### Quality Control Process
- **Setup**: Item Quality Control Specifications define test parameters per item/type
- **Execution**: Quality Control Lines populated from specifications, tested via Line Details
- **Status Logic**: Acceptance/Rejection based on Min/Max values, exact values, or selection criteria
- **Package Impact**: QC results update Package No. Information status

### Package Management
- **Creation**: Via `PackageCreationSMK` during Combined Receiving
- **Splitting**: `PackageSplitManagementSMK` handles partial quantity requirements
- **Transfer**: `SumikaPackageTransMgtSMK` manages location transfers with QC validation

### Combined Operations
- **Receiving**: Groups multiple purchase orders for batch processing
- **Shipment**: Consolidates sales orders with automatic package allocation

## Performance Considerations

### Report Optimization
- Use `SetLoadFields()` to limit database retrieval (see PERFORMANCE_FIX_SUMMARY.md)
- Avoid nested queries in report columns - use FlowFields instead
- Essential fields only: prefer 10-15 columns over 200+

### Database Patterns
- FlowFields for calculated values (e.g., `"Cost Amt. (Actual) by Date SMK"`)
- GetRecordOnce() pattern for setup tables to minimize database calls
- Proper indexing on frequently filtered fields

## Setup & Configuration

### Required Setup
- **Sumika Setup SMK**: Central configuration for number series and journal templates
- **Item Tracking Code**: Must have "Package Specific Tracking" enabled
- **Item Category**: "Auto. Quality Control Doc. SMK" controls automatic QC document creation

### Number Series
- Combined Receiving, Package Transfer, Quality Control, Package Split all require dedicated number series

## Testing & Quality Assurance

### Code Analysis Rules
- Follows `infotek.ruleset.json` - warnings suppressed for performance optimizations
- Focus on cyclomatic complexity and maintainability
- Permission set validation via `SumikaPSSMK`

### Translation Support
- Features "TranslationFile" - all labels should be translatable
- XLF files in Translations folder

## Common Integration Points

### Standard BC Extensions
- Extends core Purchase/Sales documents with SMK fields
- Leverages Item Tracking framework for package management
- Uses standard Item Journal for inventory movements

### Page Extensions Pattern
- Minimal UI changes to standard pages
- Custom pages for SMK-specific workflows
- SubPage pattern for master-detail relationships

## Development Guidelines

### Performance First
- Always consider database impact when adding fields
- Test with realistic data volumes
- Use SetLoadFields for reports and complex queries

### Error Handling
- Clear, context-specific error messages
- Validation in table triggers and management codeunits
- TestField() for required data validation

### Maintainability
- Single-responsibility codeunits
- Consistent naming patterns
- Comment complex business logic