page 60000 "Combined Receiving SMK"
{
    Caption = 'Combined Receiving';
    PageType = Document;
    SourceTable = "Combined Receiving Header SMK";
    UsageCategory = None;
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                Editable = DocumentEditable;

                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    QuickEntry = false;
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    QuickEntry = true;
                }
                field("Vendor No."; Rec."Vendor No.")
                {
                    ApplicationArea = All;
                    QuickEntry = true;
                    ShowMandatory = true;
                }
                field("Vendor Name"; Rec."Vendor Name")
                {
                    QuickEntry = true;
                    ApplicationArea = All;
                }
                field("Purchase Order No."; Rec."Purchase Order No.")
                {
                    ApplicationArea = All;
                }
                field("Created At"; Rec."Created At")
                {
                    ApplicationArea = All;
                    QuickEntry = true;
                }
                field("Created By"; Rec."Created By")
                {
                    ApplicationArea = All;
                    QuickEntry = true;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                    QuickEntry = true;
                    ShowMandatory = true;
                }
                field("Vendor Receipt No."; Rec."Vendor Shipment No.")
                {
                    ApplicationArea = All;
                    QuickEntry = true;
                    ShowMandatory = true;
                }
                field("Total Received Quantity"; Rec."Total Package Quantity")
                {
                    ApplicationArea = All;
                }
            }
            group("Label Reading Area")
            {
                Caption = 'Label Reading Area';
                Visible = false;
                field(LabelText; LabelText)
                {
                    ApplicationArea = All;
                    Caption = 'Label';
                    ToolTip = 'Specifies the value of the Label field.';
                    trigger OnValidate()
                    begin
                        //CombinedReceivingManagement.ProcessLabel(Rec, LabelText, Quantity);
                        CurrPage.Update();
                    end;
                }
                field(Quantity; Quantity)
                {
                    ApplicationArea = All;
                    Caption = 'Quantity';
                    ToolTip = 'Specifies the value of the Quantity field.';
                    trigger OnValidate()
                    begin
                        //CombinedReceivingManagement.ProcessLabel(Rec, LabelText, Quantity);
                        CurrPage.Update();
                    end;
                }
            }
            part(PackageReceivingSubpage; "Combined Receiving Subpage SMK")
            {
                ApplicationArea = All;
                Caption = 'Lines';
                Editable = DocumentEditable;
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }
        }
    }
    actions
    {
        area(Processing)
        {
            // action("Complete Receiving")
            // {
            //     ApplicationArea = All;
            //     Caption = 'Complete Receiving';
            //     Promoted = true;
            //     PromotedCategory = Process;
            //     PromotedIsBig = true;
            //     Image = Completed;
            //     PromotedOnly = true;
            //     ToolTip = 'Executes the Complete Receiving action.';

            //     trigger OnAction()
            //     begin
            //         DamteksPurchaseManagement.CheckNotReceivedPackageCount(Rec);
            //         Rec.Validate(Status, Rec.Status::Received);
            //     end;
            // }
            action(EmailTest)
            {
                ApplicationArea = All;
                Caption = 'Email Test';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Email;
                PromotedOnly = true;
                ToolTip = 'Executes the Email Test action.';
                Visible = false;

                trigger OnAction()
                var
                    CombinedReceivingLine: Record "Combined Receiving Line SMK";

                begin
                    CombinedReceivingLine.SetRange("Document No.", Rec."No.");
                    CombinedReceivingLine.SetFilter("Line Package Quantity", '>0');
                    if CombinedReceivingLine.FindSet() then
                        repeat
                            //CombinedReceivingLine.Mark(true);
                            SumikaPurchaseManagement.EmailFromCombinedReceivingLine(CombinedReceivingLine);
                        //CombinedReceivingLine.ClearMarks();
                        until CombinedReceivingLine.Next() = 0;
                end;
            }
            action(GetOrderLines)
            {
                ApplicationArea = All;
                Caption = 'Get Order Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = GetLines;
                PromotedOnly = true;
                ToolTip = 'Executes the Get Order Lines action.';

                trigger OnAction()
                begin
                    SumikaPurchaseManagement.PopulateCombinedReceivingLines(Rec);
                end;
            }

            action(ViewHeaderComments)
            {
                ApplicationArea = All;
                Caption = 'Purchase Order Comments';
                Image = ViewComments;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'View comments for the purchase order header.';

                trigger OnAction()
                var
                    PurchCommentLine: Record "Purch. Comment Line";
                    PurchCommentSheet: Page "Purch. Comment Sheet";
                begin
                    PurchCommentLine.SetRange("Document Type", PurchCommentLine."Document Type"::Order);
                    PurchCommentLine.SetRange("No.", Rec."Purchase Order No.");
                    PurchCommentLine.SetRange("Document Line No.", 0);  // 0 means header comments

                    PurchCommentSheet.SetTableView(PurchCommentLine);
                    PurchCommentSheet.RunModal();
                end;
            }

            action(Receive)
            {
                ApplicationArea = All;
                Caption = 'Receive';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostDocument;
                PromotedOnly = true;
                ToolTip = 'Executes the Receive action.';

                trigger OnAction()
                begin
                    SumikaPurchaseManagement.ReceiveCombinedReceivingHeader(Rec);
                    CurrPage.Update();
                end;
            }
        }
        // area(Reporting)
        // {
        //     action(ReceivingReceipt)
        //     {
        //         ApplicationArea = All;
        //         Caption = 'Receiving Receipt';
        //         Promoted = true;
        //         PromotedCategory = Process;
        //         PromotedIsBig = true;
        //         Image = Receipt;
        //         PromotedOnly = true;
        //         ToolTip = 'Executes the Receiving Receipt action.';

        //         trigger OnAction()
        //         begin
        //             Rec.SetRecFilter();
        //             Report.Run(Report::"Receiving Receipt", true, false, Rec);
        //         end;
        //     }
        // }
    }
    trigger OnAfterGetCurrRecord()
    begin
        DocumentEditable := true;
        if Rec.Status = Rec.Status::Received then
            DocumentEditable := false;
    end;

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        DocumentEditable: Boolean;
        LabelText: Code[20];
        Quantity: Integer;
}
