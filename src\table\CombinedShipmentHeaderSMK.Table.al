table 60016 "Combined Shipment Header SMK"
{
    Caption = 'Combined Shipment Header';
    LookupPageId = "Combined Shipment List SMK";
    DrillDownPageId = "Combined Shipment List SMK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            begin
                Clear(NoSeriesManagement);
                if "No." <> xRec."No." then begin
                    SumikaSetup.Get();
                    NoSeriesManagement.TestManual(SumikaSetup."Combined Shipment No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Sales Order No."; Code[20])
        {
            Caption = 'Sales Order No.';
            TableRelation = "Sales Header"."No." where("Document Type" = const(Order), Status = const(Released));
            ToolTip = 'Specifies the value of the Sales Order No. field.';
            trigger OnValidate()
            var
                SalesHeader: Record "Sales Header";
            begin
                if SalesHeader.Get(SalesHeader."Document Type"::Order, "Sales Order No.") then
                    Rec.Validate("Customer No.", SalesHeader."Sell-to Customer No.")
                else
                    Rec.Validate("Customer No.", '');
            end;
        }
        field(5; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            AllowInCustomizations = Never;
        }
        field(6; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(7; "Transferred to Sales Order"; Boolean)
        {
            Caption = 'Transferred to Sales Order';
            Editable = false;
            ToolTip = 'Specifies the value of the Shipped field.';
        }
        field(8; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            Editable = false;
            ToolTip = 'Specifies the value of the Customer No. field.';
            trigger OnValidate()
            var
                Customer: Record Customer;
            begin
                Customer.Get("Customer No.");
                "Customer Name" := Customer.Name;
            end;
        }
        field(9; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Customer Name field.';
        }
#pragma warning disable AA0232
        field(10; "Total Package Count"; Integer)
#pragma warning restore AA0232
        {
            Caption = 'Total Package Count';
            FieldClass = FlowField;
            CalcFormula = count("CombinedShipmentLineDtl SMK" where("Document No." = field("No.")));
            Editable = false;
            ToolTip = 'Specifies the value of the Total Package Quantity field.';
        }
        field(11; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the value of the Barcode field.';
            trigger OnValidate()
            begin
                SumikaSalesManagement.ProcessLabel(Rec);
            end;
        }
        // field(12; "Shipment Location Code"; Code[10])
        // {
        //     Caption = 'Shipment Location Code';
        //     TableRelation = Location.Code;
        // }
        // field(13; "Shipment Bin Code"; Code[20])
        // {
        //     Caption = 'Shipment Bin Code';
        //     TableRelation = Bin.Code where("Location Code" = field("Shipment Location Code"));
        // }
        field(14; "Posted Shipment No."; Code[20])
        {
            Caption = 'Posted Shipment No.';
            TableRelation = "Sales Shipment Header";
            Editable = false;
            ToolTip = 'Specifies the number of the posted sales shipment created from this combined shipment.';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    begin
        Clear(NoSeriesManagement);
        // if "No." = '' then begin
        //     SumikaSetup.Get();
        //     SumikaSetup.TestField("Combined Shipment No. Series");
        //     NoSeriesManagement.InitSeries(SumikaSetup."Combined Shipment No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            SumikaSetup.Get();
            SumikaSetup.TestField("Combined Shipment No. Series");
            "No. Series" := SumikaSetup."Combined Shipment No. Series";
            if NoSeriesManagement.AreRelated(SumikaSetup."Combined Shipment No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

        Rec.Validate("Posting Date", WorkDate());
    end;

    trigger OnDelete()
    var
        CombinedShipmentLine: Record "Combined Shipment Line SMK";
        CombinedShipmentLineDetail: Record "CombinedShipmentLineDtl SMK";
    begin
        Rec.TestField("Transferred to Sales Order", false);

        CombinedShipmentLine.SetRange("Document No.", Rec."No.");
        CombinedShipmentLine.DeleteAll(true);

        CombinedShipmentLineDetail.SetRange("Document No.", Rec."No.");
        CombinedShipmentLineDetail.DeleteAll(true);
    end;

    var
        SumikaSetup: Record "Sumika Setup SMK";
        NoSeriesManagement: Codeunit "No. Series";
        SumikaSalesManagement: Codeunit "Sumika Sales Management SMK";
}
