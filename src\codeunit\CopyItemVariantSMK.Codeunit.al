codeunit 60009 "Copy Item Variant SMK"
{
    TableNo = "Item Variant";

    trigger OnRun()
    begin
        //Rec.FindSet(false);
        //repeat
        if not NewItemVariant.Get(Rec."Item No.", Rec.Code) then begin
            NewItemVariant.Init();
            NewItemVariant.TransferFields(Rec);
            NewItemVariant.Insert(true);
        end
        else begin
            NewItemVariant.TransferFields(Rec);
            NewItemVariant.Modify(true);
        end;
        //until Rec.Next() = 0;
    end;

    var
        NewItemVariant: Record "Item Variant";
}