table 60020 "Packaging Type SMK"
{
    Caption = 'Packaging Type';
    DataClassification = CustomerContent;
    LookupPageId = "Packaging Type List SMK";
    DrillDownPageId = "Packaging Type List SMK";

    fields
    {
        field(1; Code; Code[100])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; "Allow Package Split"; Boolean)
        {
            Caption = 'Allow Package Split';
            ToolTip = 'Specifies whether packages of this type can be split into smaller quantities.';
        }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
}