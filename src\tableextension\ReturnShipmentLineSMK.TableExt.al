tableextension 60019 "Return Shipment Line SMK" extends "Return Shipment Line"
{
    fields
    {
        field(60003; "Pay-to Name SMK"; Text[100])
        {
            Caption = 'Pay-to Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Return Shipment Header"."Pay-to Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Pay-to Name field.';
        }
        field(60001; "Vendor Authorization No. SMK"; Code[35])
        {
            Caption = 'Vendor Authorization No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Return Shipment Header"."Vendor Authorization No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Vendor Authorization No. field.';
        }
    }
}