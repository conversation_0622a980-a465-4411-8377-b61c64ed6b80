tableextension 60009 "Prod. Order Line SMK" extends "Prod. Order Line"
{
    fields
    {
        field(60000; "Lot No. SMK"; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(60001; "Package Count SMK"; Integer)
        {
            Caption = 'Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Prod. Order Line Detail SMK" where("Production Order No." = field("Prod. Order No."), "Production Order Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Package Count field.';
        }
        field(60002; "Parent Package Count SMK"; Integer)
        {
            Caption = 'Parent Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Prod. Order Line Detail SMK" where("Production Order No." = field("Prod. Order No."), "Production Order Line No." = field("Line No."), "Parent Package No." = const('')));
            ToolTip = 'Specifies the value of the Package Count field.';
        }
        field(60003; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("Item No.")));
            ToolTip = 'Specifies the value of the Packaging Type field.';
        }
        // field(60004; "Machine No. SMK"; Code[10])
        // {
        //     Caption = 'Machine No.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Production Order"."Machine No. SMK" where(Status = field(Status), "No." = field("Prod. Order No.")));
        // }

    }
}