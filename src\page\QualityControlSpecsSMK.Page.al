page 60010 "Quality Control Specs. SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control Specifications';
    PageType = List;
    SourceTable = "Quality Control Spec. SMK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Code; Rec.Code)
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field(Standard; Rec.Standard)
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CopyQualityControlSpecs)
            {
                ApplicationArea = All;
                Caption = 'Copy Quality Control Specs.';
                Promoted = true;
                PromotedCategory = Process;
                ToolTip = 'Executes the Copy Quality Control Specs. action.';
                Image = CopyForecast;
                PromotedOnly = true;
                PromotedIsBig = true;
                trigger OnAction()
                var
                    QualityControlSpec: Record "Quality Control Spec. SMK";
                begin
                    CurrPage.SetSelectionFilter(QualityControlSpec);
                    DataSyncManagement.CopyQualityControlSpecs(QualityControlSpec);
                end;
            }
        }
    }
    var
        DataSyncManagement: Codeunit "Data Sync. Management SMK";
}