permissionset 60000 "Sumika PS SMK"
{
    Caption = 'Sumika Customizations', MaxLength = 30;
    Permissions = table "Barrel SMK" = X,
        tabledata "Barrel SMK" = RIMD,
        table "Combined Receiving Header SMK" = X,
        tabledata "Combined Receiving Header SMK" = RIMD,
        table "CombinedReceivingLineDtl SMK" = X,
        tabledata "CombinedReceivingLineDtl SMK" = RIMD,
        table "Combined Receiving Line SMK" = X,
        tabledata "Combined Receiving Line SMK" = RIMD,
        table "Combined Shipment Header SMK" = X,
        tabledata "Combined Shipment Header SMK" = RIMD,
        table "CombinedShipmentLineDtl SMK" = X,
        tabledata "CombinedShipmentLineDtl SMK" = RIMD,
        table "Combined Shipment Line SMK" = X,
        tabledata "Combined Shipment Line SMK" = RIMD,
        table "Dimension - G/L Acc. Setup SMK" = X,
        tabledata "Dimension - G/L Acc. Setup SMK" = RIMD,
        table "Item Quality Control Spec. SMK" = X,
        tabledata "Item Quality Control Spec. SMK" = RIMD,
        table "Package Creation SMK" = X,
        tabledata "Package Creation SMK" = RIMD,
        table "Package Transfer Header SMK" = X,
        tabledata "Package Transfer Header SMK" = RIMD,
        table "Package Transfer Line SMK" = X,
        tabledata "Package Transfer Line SMK" = RIMD,
        table "Prod. Order Line Detail SMK" = X,
        tabledata "Prod. Order Line Detail SMK" = RIMD,
        table "Production Parameter SMK" = X,
        tabledata "Production Parameter SMK" = RIMD,
        table "Quality Control Header SMK" = X,
        tabledata "Quality Control Header SMK" = RIMD,
        table "Quality Control Line Dtl. SMK" = X,
        tabledata "Quality Control Line Dtl. SMK" = RIMD,
        table "Quality Control Line SMK" = X,
        tabledata "Quality Control Line SMK" = RIMD,
        table "Quality Control Spec. SMK" = X,
        tabledata "Quality Control Spec. SMK" = RIMD,
        table "Sumika Setup SMK" = X,
        tabledata "Sumika Setup SMK" = RIMD,
        table "Temperature Parameter SMK" = X,
        tabledata "Temperature Parameter SMK" = RIMD,
        codeunit "Dimension - G/L Acc. Mgt. SMK" = X,
        codeunit "Sumika Package Trans. Mgt. SMK" = X,
        codeunit "Sumika Production Mgt. SMK" = X,
        codeunit "Sumika Purchase Management SMK" = X,
        codeunit "Sumika Quality Ctrl. Mgt. SMK" = X,
        codeunit "Sumika Sales Management SMK" = X,
        page "Barrels SMK" = X,
        page "Combined Receiving LineDetail" = X,
        page "Combined Receiving List SMK" = X,
        page "Combined Receiving SMK" = X,
        page "Combined Receiving Subpage SMK" = X,
        page "Combined Shipment Line Dtl SMK" = X,
        page "Combined Shipment Lines SMK" = X,
        page "Combined Shipment List SMK" = X,
        page "Combined Shipment SMK" = X,
        page "Copy Prod. Parameter Dialog" = X,
        page "Dimension - G/L Acc. Setup SMK" = X,
        page "Enter Test Result Dialog SMK" = X,
        page "Item Quality Control Spec. SMK" = X,
        page "New G/L Account Line SMK" = X,
        page "Package Creation Worksheet SMK" = X,
        page "Package Transfer Order SMK" = X,
        page "Package Transfer Orders SMK" = X,
        page "Package Transfer Subpage SMK" = X,
        page "Prod. Order Line Details SMK" = X,
        page "Production Parameters SMK" = X,
        page "Quality Control SMK" = X,
        page "Quality Control Specs. SMK" = X,
        page "Quality Controls SMK" = X,
        page "Quality Control Subpage SMK" = X,
        page "Quality Ctrl. Line Dtls. SMK" = X,
        page "Released Prod. Order Lines SMK" = X,
        page "Return Shipment Lines SMK" = X,
        page "Sumika Setup SMK" = X,
        page "Temperature Parameters SMK" = X,
        report "Items by Lot and Location SMK" = X,
        report "Package Label SMK" = X,
        report "Production Order Report SMK" = X,
        report "Quality Control SMK" = X,
        report "Items by Lot And Bin SMK" = X,
        page "Item Variants SMK" = X,
        tabledata "Packaging Type SMK" = RIMD,
        table "Packaging Type SMK" = X,
        page "Packaging Type List SMK" = X,
        tabledata "Package Split Header SMK" = RIMD,
        tabledata "Package Split Line SMK" = RIMD,
        table "Package Split Header SMK" = X,
        table "Package Split Line SMK" = X,
        codeunit "Package Split Management SMK" = X,
        page "Package Split List SMK" = X,
        page "Package Split SMK" = X,
        page "Package Split Subpage SMK" = X,
        tabledata "Scale SMK" = RIMD,
        table "Scale SMK" = X,
        page "Scale List SMK" = X,
        codeunit "Copy Item SMK" = X,
        codeunit "Copy Item Variant SMK" = X,
        codeunit "Data Sync. Management SMK" = X,
        codeunit "Copy Production BOM Header SMK" = X,
        codeunit "Copy Quality Control Spec. SMK" = X,
        codeunit "Copy Item Qlty. Ctrl. Spec SMK" = X,
        report "Items by Lot and Loc.-Cost SMK" = X,
        codeunit "Sumika Basic Functions SMK" = X,
        tabledata "Sumika E-Mail Setup SMK" = RIMD,
        table "Sumika E-Mail Setup SMK" = X,
        page "Sumika E-Mail Setup SMK" = X,
        page "Reservation Entry - Delete SMK" = X,
        page "Item Attribute Value Mapping" = X,
        query "Capacity Ledger Entry SMK" = X,
        query "Item Ledger Entry SMK" = X,
        query "Value Entry SMK" = X,
        page "Package Quality Control Doc." = X,
        page "Q.C. Line Dtl. Subpage SMK" = X,
        page "Package Quality Controls SMK" = X,
        page "Enter Test Result for Pkg SMK" = X,
        query "G/L Entry SMK" = X,
        codeunit "Business Event Handler SMK" = X,
        page "Item Ledger Entry Api SMK" = X;
}