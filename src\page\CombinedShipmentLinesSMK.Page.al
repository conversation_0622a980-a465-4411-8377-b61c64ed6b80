page 60027 "Combined Shipment Lines SMK"
{
    ApplicationArea = All;
    Caption = 'Lines';
    PageType = ListPart;
    SourceTable = "Combined Shipment Line SMK";


    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Source Document No."; Rec."Source Document No.")
                {
                    Editable = false;
                }
                field("Source Document Line No."; Rec."Source Document Line No.")
                {
                    Editable = false;
                }
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field("Location Code"; Rec."Location Code")
                {
                    Editable = false;
                }
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = false;
                }
                field("Qty. to Ship"; Rec."Qty. to Ship")
                {
                }
                field("Quantity Shipped"; Rec."Quantity Shipped")
                {
                    Editable = false;
                }
                field("Line Package Quantity"; Rec."Line Package Quantity")
                {
                    Editable = false;
                }
                field("Line Package Count"; Rec."Line Package Count")
                {
                    Editable = false;
                }
                field("Packaging Type SMK"; Rec."Packaging Type")
                {
                    Editable = false;
                }
            }
        }
    }
}