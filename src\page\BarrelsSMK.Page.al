page 60008 "Barrels SMK"
{
    ApplicationArea = All;
    Caption = 'Barrels';
    PageType = List;
    SourceTable = "Barrel SMK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
            }
        }
    }
    trigger OnOpenPage()
    var
        ReservationEntry: Record "Reservation Entry";
    begin
        ReservationEntry.DeleteAll(false);
    end;
}