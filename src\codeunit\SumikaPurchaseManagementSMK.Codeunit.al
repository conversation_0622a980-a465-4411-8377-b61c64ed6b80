codeunit 60001 "Sumika Purchase Management SMK"
{
    procedure PopulateCombinedReceivingLines(var CombinedReceivingHeader: Record "Combined Receiving Header SMK")
    var
        CombinedReceivingLine: Record "Combined Receiving Line SMK";
        Purchaseheader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        PurchasesPayablesSetup: Record "Purchases & Payables Setup";

    begin
        PurchasesPayablesSetup.Get();
        PurchasesPayablesSetup.TestField("Default Qty. to Receive", PurchasesPayablesSetup."Default Qty. to Receive"::Blank);
        CombinedReceivingLine.SetRange("Document No.", CombinedReceivingHeader."No.");
        CombinedReceivingLine.DeleteAll(true);

        CombinedReceivingHeader.TestField(Status, CombinedReceivingHeader.Status::New);

        Purchaseheader.SetRange("Document Type", Purchaseheader."Document Type"::Order);
        Purchaseheader.SetRange("Buy-from Vendor No.", CombinedReceivingHeader."Vendor No.");
#pragma warning disable AA0210
        Purchaseheader.SetRange("Completely Received", false);
#pragma warning restore AA0210
        Purchaseheader.SetRange(Status, Purchaseheader.Status::Released);

        if CombinedReceivingHeader."Purchase Order No." <> '' then
            Purchaseheader.SetRange("No.", CombinedReceivingHeader."Purchase Order No.");

        Purchaseheader.FindSet();
        repeat
            PurchaseLine.SetRange("Document Type", Purchaseheader."Document Type");
            PurchaseLine.SetRange("Document No.", Purchaseheader."No.");
            //PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
            PurchaseLine.SetFilter(Type, '%1|%2|%3|%4', PurchaseLine.Type::Item, PurchaseLine.Type::"Fixed Asset", PurchaseLine.Type::"G/L Account", PurchaseLine.Type::"Charge (Item)");
            PurchaseLine.SetRange("Completely Received", false);
            if PurchaseLine.FindSet(false) then
                repeat
                    if PurchaseLine.Type = PurchaseLine.Type::Item then
                        PurchaseLine.TestField("Bin Code");

                    CombinedReceivingLine.Init();
                    CombinedReceivingLine."Document No." := CombinedReceivingHeader."No.";
                    CombinedReceivingLine.Insert(true);
                    CombinedReceivingLine.Validate("Source Document No.", PurchaseLine."Document No.");
                    CombinedReceivingLine.Validate("Source Document Line No.", PurchaseLine."Line No.");
                    CombinedReceivingLine.Validate("Item No.", PurchaseLine."No.");
                    CombinedReceivingLine.Validate("Variant Code", PurchaseLine."Variant Code");
                    CombinedReceivingLine.Validate("Item Description", PurchaseLine.Description);
                    CombinedReceivingLine.Validate("Unit of Measure Code", PurchaseLine."Unit of Measure Code");
                    CombinedReceivingLine.Validate(Quantity, PurchaseLine.Quantity);
                    CombinedReceivingLine.Validate("Quantity Received", PurchaseLine."Quantity Received");
                    CombinedReceivingLine.Validate("Location Code", PurchaseLine."Location Code");
                    CombinedReceivingLine.Validate("Bin Code", PurchaseLine."Bin Code");
                    CombinedReceivingLine.Validate("Planned Receipt Date", PurchaseLine."Planned Receipt Date");
                    if PurchaseLine.Type <> PurchaseLine.Type::Item then
                        CombinedReceivingLine.Validate("Qty. to Receive Manual", PurchaseLine."Outstanding Quantity");
                    CombinedReceivingLine.Modify(true);
                until PurchaseLine.Next() = 0;
        until Purchaseheader.Next() = 0;
    end;

    procedure ReceiveCombinedReceivingHeader(var CombinedReceivingHeader: Record "Combined Receiving Header SMK")
    var
        CombinedReceivingLine: Record "Combined Receiving Line SMK";
        PurchaseHeader: Record "Purchase Header";
        PurchaseLine: Record "Purchase Line";
        CombinedReceivingLineDtl: Record "CombinedReceivingLineDtl SMK";
        PurchPost: Codeunit "Purch.-Post";
        NoLinesErr: Label 'There is no line to receive. Check Qty. to Receive/Manual fields.';
        ModifyPurchaseHeader: Boolean;
    begin
        CombinedReceivingHeader.TestField(Status, CombinedReceivingHeader.Status::New);
        CombinedReceivingHeader.TestField("Posting Date");
        CombinedReceivingHeader.TestField("Vendor Shipment No.");

        CombinedReceivingLine.SetRange("Document No.", CombinedReceivingHeader."No.");
        if not CombinedReceivingLine.FindSet() then
            Error(NoLinesErr);
        repeat
            PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, CombinedReceivingLine."Source Document No.");
            PurchaseLine.Get(PurchaseLine."Document Type"::Order, CombinedReceivingLine."Source Document No.", CombinedReceivingLine."Source Document Line No.");
            ModifyPurchaseHeader := false;

            if IsPackageTrackingEnabled(CombinedReceivingLine."Item No.") then begin
                CombinedReceivingLineDtl.Reset();
                CombinedReceivingLineDtl.SetRange("Document No.", CombinedReceivingLine."Document No.");
                CombinedReceivingLineDtl.SetRange("Document Line No.", CombinedReceivingLine."Line No.");
                CombinedReceivingLineDtl.SetRange("Parent Package No.", '');
                if CombinedReceivingLineDtl.FindSet() then
                    repeat
                        PurchaseLine.Validate("Qty. to Receive", PurchaseLine."Qty. to Receive" + CombinedReceivingLineDtl."Qty. to Receive");
                        PurchaseLine.Modify(true);
                        ModifyPurchaseHeader := true;

                        AssignLotNoAndPackageNoToPurchaseLine(PurchaseLine, CombinedReceivingLineDtl."Qty. to Receive", CombinedReceivingLineDtl."Lot No.", CombinedReceivingLineDtl."Package No.");
                    until CombinedReceivingLineDtl.Next() = 0;

                CombinedReceivingLine.CalcFields("Line Package Quantity");
                if CombinedReceivingLine."Line Package Quantity" > 0 then
                    EmailFromCombinedReceivingLine(CombinedReceivingLine);
            end
            else
                if CombinedReceivingLine."Qty. to Receive Manual" > 0 then begin
                    PurchaseLine.Validate("Qty. to Receive", CombinedReceivingLine."Qty. to Receive Manual");
                    PurchaseLine.Modify(true);
                    ModifyPurchaseHeader := true;
                    AssignLotNoAndPackageNoToPurchaseLine(PurchaseLine, CombinedReceivingLine."Qty. to Receive Manual", CombinedReceivingLine."Lot No.", '');
                    EmailFromCombinedReceivingLine(CombinedReceivingLine);
                end;

            if ModifyPurchaseHeader then begin
                PurchaseHeader.Validate("Combined Receiving No. SMK", CombinedReceivingLine."Document No.");
                PurchaseHeader."Posting Date" := CombinedReceivingHeader."Posting Date";
                PurchaseHeader.Validate("VAT Reporting Date", CombinedReceivingHeader."Posting Date");
                PurchaseHeader.Validate("Vendor Shipment No.", CombinedReceivingHeader."Vendor Shipment No.");
                PurchaseHeader.Modify(true);
            end;
        until CombinedReceivingLine.Next() = 0;

        PurchaseHeader.Reset();
        PurchaseHeader.SetRange("Document Type", PurchaseHeader."Document Type"::Order);
        PurchaseHeader.SetRange("Combined Receiving No. SMK", CombinedReceivingHeader."No.");
        PurchaseHeader.FindSet();
        repeat
            Clear(PurchPost);
            PurchaseHeader.Receive := true;
            PurchaseHeader.Invoice := false;
            PurchPost.Run(PurchaseHeader);
        until PurchaseHeader.Next() = 0;

        CombinedReceivingHeader.Validate(Status, CombinedReceivingHeader.Status::Received);
        CombinedReceivingHeader.Modify(true);
    end;

    local procedure AssignLotNoAndPackageNoToPurchaseLine(PurchaseLine: Record "Purchase Line"; Qty: Decimal; LotNo: Code[50]; PackageNo: Code[50])
    var
        // Item: Record Item;
        // ItemTrackingCode: Record "Item Tracking Code";
        TempReservEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservStatus: Enum "Reservation Status";
    begin
        if PurchaseLine.Type <> PurchaseLine.Type::Item then
            exit;

        TempReservEntry.Init();
        TempReservEntry."Entry No." := 1;
        //TempReservEntry."Item No." := PurchaseLine."No.";//For Label Report
        //TempReservEntry."Variant Code" := PurchaseLine."Variant Code";//For Label Report
        //TempReservEntry.Description := PurchaseLine.Description;//For Label Report
        TempReservEntry.Quantity := Qty;
        //TempReservEntry."Serial No." := NoSeriesManagement.GetNextNo(Item."Serial Nos.", WorkDate(), true);
        TempReservEntry."Lot No." := LotNo;
        TempReservEntry."Package No." := PackageNo;

        TempReservEntry.Insert(false);

        //CreateReservEntry.SetDates(0D, TempReservEntry."Expiration Date");

        CreateReservEntry.CreateReservEntryFor(Database::"Purchase Line", PurchaseLine."Document Type".AsInteger(),
                                                PurchaseLine."Document No.", '', 0, PurchaseLine."Line No.", PurchaseLine."Qty. per Unit of Measure",
                                                TempReservEntry.Quantity, TempReservEntry.Quantity * PurchaseLine."Qty. per Unit of Measure", TempReservEntry);

        CreateReservEntry.CreateEntry(PurchaseLine."No.", PurchaseLine."Variant Code", PurchaseLine."Location Code", '',
                                        PurchaseLine."Expected Receipt Date", 0D, 0, ReservStatus::Surplus);

    end;

    procedure AssignLotNo(var CombinedReceivingLine: Record "Combined Receiving Line SMK")
    var
        Location: Record Location;
        Item: Record Item;
        NoSeriesMgt: Codeunit "No. Series";
    //AlreadyAssignedErr: Label 'Lot No. is already assigned for selected line.';
    begin
        if CombinedReceivingLine."Lot No." <> '' then
            exit;

        CombinedReceivingLine.TestField("Bin Code");

        CombinedReceivingLine.TestField("Location Code");
        if GetVendorLotNoMandatoryFromPurchaseOrderNo(CombinedReceivingLine."Source Document No.") then
            CombinedReceivingLine.TestField("Vendor Lot No.");

        Location.Get(CombinedReceivingLine."Location Code");
        //Location.TestField("Item Lot Nos. SMK");
        if Location."Item Lot Nos. SMK" <> '' then
            CombinedReceivingLine.Validate("Lot No.", NoSeriesMgt.GetNextNo(Location."Item Lot Nos. SMK", WorkDate(), true))
        else begin
            Item.Get(CombinedReceivingLine."Item No.");
            Item.TestField("Lot Nos.");
            CombinedReceivingLine.Validate("Lot No.", NoSeriesMgt.GetNextNo(Item."Lot Nos.", WorkDate(), true))
        end;
        CombinedReceivingLine.Modify(true);

        CreateLotNoInformationFromCombinedReceivingLine(CombinedReceivingLine);
    end;

    procedure PopulateAndOpenPackageCreation(CombinedReceivingLine: Record "Combined Receiving Line SMK"; PackageCreationType: Enum "Package Creation Type SMK")
    var
        CombinedReceivingHeader: Record "Combined Receiving Header SMK";
        TempPackageCreation: Record "Package Creation SMK" temporary;
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        ReceivedErr: Label 'Can not create package for received documents.';
    begin
        CombinedReceivingHeader.Get(CombinedReceivingLine."Document No.");
        if CombinedReceivingHeader.Status = CombinedReceivingHeader.Status::Received then
            Error(ReceivedErr);

        CombinedReceivingLine.TestField("Bin Code");

        if CombinedReceivingLine."Lot No." = '' then
            AssignLotNo(CombinedReceivingLine);

        Item.Get(CombinedReceivingLine."Item No.");
        Item.TestField("Item Tracking Code");
        ItemTrackingCode.Get(Item."Item Tracking Code");
        ItemTrackingCode.TestField("Package Specific Tracking");

        TempPackageCreation.Init();
        TempPackageCreation.Insert(false);
#pragma warning disable LC0078
        TempPackageCreation.Validate(Type, PackageCreationType);
#pragma warning restore LC0078
        TempPackageCreation."Item No." := CombinedReceivingLine."Item No.";
        TempPackageCreation."Variant Code" := CombinedReceivingLine."Variant Code";
        TempPackageCreation."Item Description" := CombinedReceivingLine."Item Description";
        TempPackageCreation."Lot No." := CombinedReceivingLine."Lot No.";
        TempPackageCreation."Source Document No." := CombinedReceivingLine."Document No.";
        TempPackageCreation."Source Document Line No." := CombinedReceivingLine."Line No.";
        TempPackageCreation."Entry Type" := TempPackageCreation."Entry Type"::Purchase;
        TempPackageCreation.Modify(false);

        Page.Run(Page::"Package Creation Worksheet SMK", TempPackageCreation);
    end;

    procedure UpdatePackageCreationQuantities(var PackageCreation: Record "Package Creation SMK"; CurrFieldNo: Integer)
    begin
        case PackageCreation.Type of
            PackageCreation.Type::Single:
                begin
                    PackageCreation.Validate("Number of Packages", 1);

                    case CurrFieldNo of
                        PackageCreation.FieldNo("Qty. to Receive"):
                            PackageCreation."Package Quantity" := PackageCreation."Qty. to Receive";
                        PackageCreation.FieldNo("Package Quantity"):
                            PackageCreation."Qty. to Receive" := PackageCreation."Package Quantity";
                        PackageCreation.FieldNo(Type):
                            begin
                                PackageCreation."Qty. to Receive" := 0;
                                PackageCreation."Package Quantity" := 0;
                            end;
                    end;
                end;
            PackageCreation.Type::Multiple, PackageCreation.Type::"Multiple without Palette":
                begin
                    case CurrFieldNo of
                        PackageCreation.FieldNo(Type):
                            begin
                                PackageCreation."Qty. to Receive" := 0;
                                PackageCreation."Package Quantity" := 0;
                                PackageCreation."Number of Packages" := 0;
                            end;
                    end;

                    if (PackageCreation."Qty. to Receive" <> 0) and (PackageCreation."Package Quantity" <> 0) then
                        PackageCreation.Validate("Number of Packages", Round(PackageCreation."Qty. to Receive" / PackageCreation."Package Quantity", 1, '>'));
                end;
        end;
    end;

    procedure IsPackageTrackingEnabled(ItemNo: Code[20]): Boolean
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
    begin
        if not Item.Get(ItemNo) then
            exit(false);

        //Item.TestField("Item Tracking Code");
        if Item."Item Tracking Code" = '' then
            exit(false);

        ItemTrackingCode.Get(Item."Item Tracking Code");

        exit(ItemTrackingCode."Package Specific Tracking");
    end;

    procedure CreatePackagesFromPackageCreation(var TempPackageCreation: Record "Package Creation SMK" temporary)
    var
        CombinedReceivingLine: Record "Combined Receiving Line SMK";
        CombinedReceivingLineDtl: Record "CombinedReceivingLineDtl SMK";
        ProdOrderLine: Record "Prod. Order Line";
        ProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
        SuccesfulCreatedMsg: Label '%1 Package succesfuly created.', Comment = '%1 is number of packages created';
        RemainingQtytoReceive: Decimal;
        i: Integer;
        PackageQty: Decimal;
    begin
        TempPackageCreation.TestField("Qty. to Receive");
        TempPackageCreation.TestField("Package Quantity");
        TempPackageCreation.TestField("Number of Packages");

        case TempPackageCreation."Entry Type" of
            TempPackageCreation."Entry Type"::Purchase:
                begin
                    CombinedReceivingLine.Get(TempPackageCreation."Source Document No.", TempPackageCreation."Source Document Line No.");

                    case TempPackageCreation.Type of
                        TempPackageCreation.Type::Single:
                            begin
                                PackageQty := TempPackageCreation."Package Quantity";

                                CreatePackage(TempPackageCreation, CombinedReceivingLine, PackageQty);

                                i := 1;
                            end;
                        TempPackageCreation.Type::Multiple:
                            begin
                                SumikaProductionMgt.AssignParentPackageNo(TempPackageCreation, CombinedReceivingLine);

                                RemainingQtytoReceive := TempPackageCreation."Qty. to Receive";

                                repeat
                                    if RemainingQtytoReceive < TempPackageCreation."Package Quantity" then
                                        PackageQty := RemainingQtytoReceive
                                    else
                                        PackageQty := TempPackageCreation."Package Quantity";

                                    CreatePackage(TempPackageCreation, CombinedReceivingLine, PackageQty);

                                    RemainingQtytoReceive -= TempPackageCreation."Package Quantity";
                                    i += 1;
                                until (i = TempPackageCreation."Number of Packages");
                            end;
                        TempPackageCreation.Type::"Multiple without Palette":
                            begin
                                RemainingQtytoReceive := TempPackageCreation."Qty. to Receive";

                                repeat
                                    if RemainingQtytoReceive < TempPackageCreation."Package Quantity" then
                                        PackageQty := RemainingQtytoReceive
                                    else
                                        PackageQty := TempPackageCreation."Package Quantity";

                                    CreatePackage(TempPackageCreation, CombinedReceivingLine, PackageQty);

                                    RemainingQtytoReceive -= TempPackageCreation."Package Quantity";
                                    i += 1;
                                until (i = TempPackageCreation."Number of Packages");
                            end;

                    end;
                    CombinedReceivingLineDtl.SetRange("Document No.", CombinedReceivingLine."Document No.");
                    CombinedReceivingLineDtl.SetRange("Document Line No.", CombinedReceivingLine."Line No.");

                    Page.Run(Page::"Combined Receiving LineDetail", CombinedReceivingLineDtl);
                end;
            TempPackageCreation."Entry Type"::Production:
                begin
                    ProdOrderLine.Get(ProdOrderLine.Status::Released, TempPackageCreation."Source Document No.", TempPackageCreation."Source Document Line No.");

                    case TempPackageCreation.Type of
                        TempPackageCreation.Type::Single:
                            begin
                                PackageQty := TempPackageCreation."Package Quantity";

                                SumikaProductionMgt.CreatePackage(TempPackageCreation, ProdOrderLine, PackageQty);

                                i := 1;

                                ProdOrderLineDetail.SetRange(Status, ProdOrderLine.Status);
                                ProdOrderLineDetail.SetRange("Production Order No.", ProdOrderLine."Prod. Order No.");
                                ProdOrderLineDetail.SetRange("Production Order Line No.", ProdOrderLine."Line No.");

                                Page.Run(Page::"Prod. Order Line Details SMK", ProdOrderLineDetail);
                            end;
                        TempPackageCreation.Type::Multiple:
                            begin
                                SumikaProductionMgt.AssignParentPackageNo(TempPackageCreation, ProdOrderLine);

                                RemainingQtytoReceive := TempPackageCreation."Qty. to Receive";

                                repeat
                                    if RemainingQtytoReceive < TempPackageCreation."Package Quantity" then
                                        PackageQty := RemainingQtytoReceive
                                    else
                                        PackageQty := TempPackageCreation."Package Quantity";

                                    //PackageQty := TempPackageCreation."Qty. to Receive";

                                    SumikaProductionMgt.CreatePackage(TempPackageCreation, ProdOrderLine, PackageQty);
                                    RemainingQtytoReceive -= TempPackageCreation."Package Quantity";
                                    i += 1;
                                until (i = TempPackageCreation."Number of Packages");

                                ProdOrderLineDetail.SetRange(Status, ProdOrderLine.Status);
                                ProdOrderLineDetail.SetRange("Production Order No.", ProdOrderLine."Prod. Order No.");
                                ProdOrderLineDetail.SetRange("Production Order Line No.", ProdOrderLine."Line No.");
                                ProdOrderLineDetail.SetRange("Parent Package No.", '');

                                Page.Run(Page::"Prod. Order Line Details SMK", ProdOrderLineDetail);
                            end;
                        TempPackageCreation.Type::"Multiple without Palette":
                            begin
                                //SumikaProductionMgt.AssignParentPackageNo(TempPackageCreation);

                                RemainingQtytoReceive := TempPackageCreation."Qty. to Receive";

                                repeat
                                    if RemainingQtytoReceive < TempPackageCreation."Package Quantity" then
                                        PackageQty := RemainingQtytoReceive
                                    else
                                        PackageQty := TempPackageCreation."Package Quantity";

                                    SumikaProductionMgt.CreatePackage(TempPackageCreation, ProdOrderLine, PackageQty);

                                    RemainingQtytoReceive -= TempPackageCreation."Package Quantity";
                                    i += 1;
                                until (i = TempPackageCreation."Number of Packages");

                                ProdOrderLineDetail.SetRange(Status, ProdOrderLine.Status);
                                ProdOrderLineDetail.SetRange("Production Order No.", ProdOrderLine."Prod. Order No.");
                                ProdOrderLineDetail.SetRange("Production Order Line No.", ProdOrderLine."Line No.");

                                Page.Run(Page::"Prod. Order Line Details SMK", ProdOrderLineDetail);
                            end;
                    end;
                end;
        end;
        Message(SuccesfulCreatedMsg, i);
    end;

    procedure AssignPackageNo(): Code[20]
    var
        InventorySetup: Record "Inventory Setup";
        NoSeriesManagement: Codeunit "No. Series";
    begin
        InventorySetup.Get();
        InventorySetup.TestField("Package Nos.");

        exit(NoSeriesManagement.GetNextNo(InventorySetup."Package Nos.", WorkDate(), true));
    end;

    local procedure CreatePackage(var TempPackageCreation: Record "Package Creation SMK" temporary; var CombinedReceivingLine: Record "Combined Receiving Line SMK"; PackageQty: Decimal)
    var
        CombinedReceivingLineDetail: Record "CombinedReceivingLineDtl SMK";
        NoSeries: Codeunit "No. Series";
    begin
        CombinedReceivingLine.TestField("Bin Code");

        SumikaSetup.GetRecordOnce();

        CombinedReceivingLineDetail.Init();
        CombinedReceivingLineDetail."Document No." := TempPackageCreation."Source Document No.";
        CombinedReceivingLineDetail."Document Line No." := TempPackageCreation."Source Document Line No.";
        CombinedReceivingLineDetail.Insert(true);
        CombinedReceivingLineDetail.Validate("Purchase Order No.", CombinedReceivingLine."Source Document No.");
        CombinedReceivingLineDetail.Validate("Purchase Order Line No.", CombinedReceivingLine."Source Document Line No.");
        CombinedReceivingLineDetail.Validate("Item No.", TempPackageCreation."Item No.");
        CombinedReceivingLineDetail.Validate("Variant Code", TempPackageCreation."Variant Code");
        CombinedReceivingLineDetail.Validate("Item Description", TempPackageCreation."Item Description");
        CombinedReceivingLineDetail.Validate("Location Code", CombinedReceivingLine."Location Code");
        CombinedReceivingLineDetail.Validate("Bin Code", CombinedReceivingLine."Bin Code");
        CombinedReceivingLineDetail.Validate("Qty. to Receive", PackageQty);
        if TempPackageCreation."Parent Package No." = '' then
            CombinedReceivingLineDetail.Validate("Package No.", AssignPackageNo())
        else
            CombinedReceivingLineDetail.Validate("Package No.", NoSeries.GetNextNo(SumikaSetup."Parent Package No. Series", WorkDate(), false));
        CombinedReceivingLineDetail.Validate("Lot No.", CombinedReceivingLine."Lot No.");
        CombinedReceivingLineDetail.Validate("Vendor Lot No.", CombinedReceivingLine."Vendor Lot No.");
        CombinedReceivingLineDetail.Validate("Parent Package No.", TempPackageCreation."Parent Package No.");
        CombinedReceivingLineDetail.Modify(true);

        CreatePackageNoInformationFromCombinedReceivingLineDetail(CombinedReceivingLineDetail);
    end;

    procedure CreatePackageNoInformationFromCombinedReceivingLineDetail(CombinedReceivingLineDetail: Record "CombinedReceivingLineDtl SMK")
    var
        PackageNoInformation: Record "Package No. Information";
        LotNoInformation: Record "Lot No. Information";
    begin
        LotNoInformation.Get(CombinedReceivingLineDetail."Item No.", CombinedReceivingLineDetail."Variant Code", CombinedReceivingLineDetail."Lot No.");

        PackageNoInformation.Init();
        PackageNoInformation."Item No." := CombinedReceivingLineDetail."Item No.";
        PackageNoInformation."Variant Code" := CombinedReceivingLineDetail."Variant Code";
        PackageNoInformation."Package No." := CombinedReceivingLineDetail."Package No.";
        PackageNoInformation.Insert(true);
        PackageNoInformation.Validate(Description, CombinedReceivingLineDetail."Item Description");
        PackageNoInformation.Validate("Lot No. SMK", CombinedReceivingLineDetail."Lot No.");
        //PackageNoInformation.Validate("Expiration Date SMK", LotNoInformation.);
        PackageNoInformation.Validate("Inventory Entry Date SMK", WorkDate());
        PackageNoInformation.Validate("Package Order No. SMK", CalculatePackageOrderNoByLotNo(PackageNoInformation));
        PackageNoInformation.Validate("Parent Package No. SMK", CombinedReceivingLineDetail."Parent Package No.");
        PackageNoInformation.Validate("Label Quantity SMK", CombinedReceivingLineDetail."Qty. to Receive");
        PackageNoInformation.Validate("Vendor Lot No. SMK", CombinedReceivingLineDetail."Vendor Lot No.");
        PackageNoInformation.Modify(true);
    end;

    procedure CalculatePackageOrderNoByLotNo(var PackageNoInformation: Record "Package No. Information"): Integer
    var
        PackageNoInformation2: Record "Package No. Information";
    begin
        // if IsChildPackage(PackageNoInformation) then begin
        //     PackageNoInformation2.Get(PackageNoInformation."Item No.", PackageNoInformation."Variant Code", PackageNoInformation."Parent Package No. SMK");
        //     exit(PackageNoInformation2."Package Order No. SMK");
        // end;


        PackageNoInformation2.SetRange("Lot No. SMK", PackageNoInformation."Lot No. SMK");
        //PackageNoInformation2.SetFilter("Production Order No. SMK", '<>''''');
        if not PackageNoInformation2.FindLast() then
            exit(1)
        else
            exit(PackageNoInformation2."Package Order No. SMK" + 1);
    end;

    procedure CreateLotNoInformationFromCombinedReceivingLine(CombinedReceivingLine: Record "Combined Receiving Line SMK")
    var
        CombinedReceivingHeader: Record "Combined Receiving Header SMK";
        LotNoInformation: Record "Lot No. Information";
    begin
        CombinedReceivingHeader.Get(CombinedReceivingLine."Document No.");

        LotNoInformation.Init();
        LotNoInformation."Item No." := CombinedReceivingLine."Item No.";
        LotNoInformation."Variant Code" := CombinedReceivingLine."Variant Code";
        LotNoInformation."Lot No." := CombinedReceivingLine."Lot No.";
        LotNoInformation.Insert(true);
        LotNoInformation.Validate(Description, CombinedReceivingLine."Item Description");
        LotNoInformation.Validate("Company No. SMK", CombinedReceivingHeader."Vendor No.");
        LotNoInformation.Validate("Company Name SMK", CombinedReceivingHeader."Vendor Name");
        LotNoInformation.Validate("Vendor Lot No. SMK", CombinedReceivingLine."Vendor Lot No.");
        LotNoInformation.Modify(true);

        SumikaQualityCtrlMgt.CreateQualityControlDocumentFromLotNoInformation(LotNoInformation, Enum::"Quality Control Type SMK"::Purchase);

    end;

    procedure GetItemDescriptionFromNoAndVariantCode(ItemNo: Code[20]; VariantCode: Code[10]): Text[100]
    var
        Item: Record Item;
        ItemVariant: Record "Item Variant";
    begin
        if ItemVariant.Get(ItemNo, VariantCode) then
            exit(ItemVariant.Description)
        else
            if Item.Get(ItemNo) then
                exit(Item.Description)
            else
                exit('');

    end;

    local procedure GetVendorLotNoMandatoryFromPurchaseOrderNo(PurchaseOrderNo: Code[20]): Boolean
    var
        PurchaseHeader: Record "Purchase Header";
        Vendor: Record Vendor;
    begin
        if not PurchaseHeader.Get(PurchaseHeader."Document Type"::Order, PurchaseOrderNo) then
            exit(false);

        if not Vendor.Get(PurchaseHeader."Buy-from Vendor No.") then
            exit(false);

        exit(Vendor."Vendor Lot No. Mandatory SMK");
    end;



    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnAfterValidateLocationCode, '', false, false)]
    local procedure OnAfterValidateLocationCode(var PurchaseLine: Record "Purchase Line"; xPurchaseLine: Record "Purchase Line")
    var
        Bin: Record Bin;
    begin
        if PurchaseLine."Bin Code" <> '' then
            exit;
        if PurchaseLine."Document Type" <> PurchaseLine."Document Type"::Order then
            exit;
        if PurchaseLine.Type <> PurchaseLine.Type::Item then
            exit;
        if PurchaseLine."Location Code" = '' then
            exit;
        if PurchaseLine.IsTemporary() then
            exit;

        Bin.SetRange("Location Code", PurchaseLine."Location Code");
        Bin.FindFirst();

        PurchaseLine.Validate("Bin Code", Bin.Code);
        PurchaseLine.Modify(true);
    end;

    procedure OnAfterValidateCompletedOnPurchaseHeader(var PurchaseHeader: Record "Purchase Header")
    var
        PurchaseLine: Record "Purchase Line";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmQst: Label 'There is at least one line that is not completely received. Do you want to continue?';
    begin
        if not PurchaseHeader."Completed SMK" then
            exit;

        if PurchaseHeader."Completely Received" then
            exit;

        if not ConfirmManagement.GetResponseOrDefault(ConfirmQst, true) then
            exit;

        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        //PurchaseLine.SetRange(Type, PurchaseLine.Type::Item);
        PurchaseLine.SetRange("Completely Received", false);
        if PurchaseLine.FindSet(true) then
            if PurchaseHeader.Status = PurchaseHeader.Status::Released then begin
                PurchaseHeader.Status := PurchaseHeader.Status::Open;
                PurchaseHeader.Modify(true);
            end;
        repeat
            PurchaseLine.Validate(Quantity, PurchaseLine."Quantity Received");
            PurchaseLine.Modify(true);
        until PurchaseLine.Next() = 0;

        PurchaseHeader.Status := PurchaseHeader.Status::Released;
        PurchaseHeader.Modify(true);

        PurchaseLine.Reset();
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        PurchaseLine.SetFilter("Quantity Received", '<>%1', 0);
        if PurchaseLine.IsEmpty() then
            PurchaseHeader.Delete(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Get Receipt", OnBeforeCopyDocumentAttachments, '', false, false)]
    local procedure OnBeforeCopyDocumentAttachments(var OrderNoList: List of [Code[20]]; var PurchaseHeader: Record "Purchase Header"; var IsHandled: Boolean)
    var
        OrderPurchaseHeader: Record "Purchase Header";
        PurchCommentLine: Record "Purch. Comment Line";
        OrderNo: Code[20];
    begin
        foreach OrderNo in OrderNoList do
            if OrderPurchaseHeader.Get(OrderPurchaseHeader."Document Type"::Order, OrderNo) then
                PurchCommentLine.CopyComments(OrderPurchaseHeader."Document Type".AsInteger(), PurchaseHeader."Document Type".AsInteger(), OrderPurchaseHeader."No.", PurchaseHeader."No.");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Purch. Comment Line", OnBeforeCopyCommentsOnBeforeInsert, '', false, false)]
    local procedure OnBeforeCopyCommentsOnBeforeInsert(var NewPurchCommentLine: Record "Purch. Comment Line"; OldPurchCommentLine: Record "Purch. Comment Line")
    var
        PurchCommentLine: Record "Purch. Comment Line";
    begin
        PurchCommentLine.SetRange("Document Type", NewPurchCommentLine."Document Type");
        PurchCommentLine.SetRange("No.", NewPurchCommentLine."No.");
        if PurchCommentLine.FindLast() then
            NewPurchCommentLine."Line No." := PurchCommentLine."Line No." + 10000;
    end;

    // procedure EmailTest(CombinedReceivingHeader: Record "Combined Receiving Header SMK")
    // var
    //     CombinedReceivingLine: Record "Combined Receiving Line SMK";
    // begin
    //     CombinedReceivingLine.SetRange("Document No.", CombinedReceivingHeader."No.");
    //     if CombinedReceivingLine.FindSet(false) then
    //         repeat
    //             EmailFromCombinedReceivingLine(CombinedReceivingLine);
    //         until CombinedReceivingLine.Next() = 0;
    // end;

    procedure EmailFromCombinedReceivingLine(var CombinedReceivingLine: Record "Combined Receiving Line SMK")
    var
        LotNoInformation: Record "Lot No. Information";
        SumikaEMailSetup: Record "Sumika E-Mail Setup SMK";
        CombinedReceivingLine2: Record "Combined Receiving Line SMK";
        Email: Codeunit Email;
        EmailMessage: Codeunit "Email Message";
        ItemNoLbl: Label 'Item No.: %1', Comment = 'Item No.: %1';
        ItemDescLbl: Label 'Description: %1', Comment = 'Description: %1';
        QuantityLbl: Label 'Quantity: %1 %2', Comment = 'Quantity: %1 %2';
        LocationLbl: Label 'Location: %1', Comment = 'Location: %1';
        LotNoLbl: Label 'Lot No.: %1', Comment = 'Lot No.: %1';
        QualityControlDocNoLbl: Label 'Quality Control Document No.: %1', Comment = 'Quality Control Document No.: %1';
        ReceivingDateTimeLbl: Label 'Receiving Date-Time: %1', Comment = 'Receiving Date-Time: %1';
        ReceivedByLbl: Label 'Received By: %1', Comment = 'Received By: %1';
        SubjectLbl: Label 'Receiving Made - %1', Comment = 'Receiving Made - %1';
        BodyText: Text;
        SubjectText: Text;
        ReceipentList: List of [Text];
        CCList: List of [Text];
        BCCList: List of [Text];
    begin
        if not SumikaEMailSetup.FindSet(false) then
            exit;

        repeat
            SumikaEMailSetup.CalcFields("E-Mail");

            CombinedReceivingLine2.CopyFilters(CombinedReceivingLine);
            CombinedReceivingLine2.SetRange("Line No.", CombinedReceivingLine."Line No.");
            CombinedReceivingLine2.SetFilter("Item No.", SumikaEMailSetup."Item No. Filter");
            CombinedReceivingLine2.SetFilter("Location Code", SumikaEMailSetup."Location Code Filter");
            if CombinedReceivingLine2.FindFirst() then begin
                CombinedReceivingLine2.CalcFields("Line Package Quantity");

                if LotNoInformation.Get(CombinedReceivingLine2."Item No.", CombinedReceivingLine2."Variant Code", CombinedReceivingLine2."Lot No.") then;//

                BodyText := StrSubstNo(ItemNoLbl, CombinedReceivingLine2."Item No.");
                BodyText += '<br>' + StrSubstNo(ItemDescLbl, CombinedReceivingLine2."Item Description");
                BodyText += '<br>' + StrSubstNo(QuantityLbl, CombinedReceivingLine2."Line Package Quantity", CombinedReceivingLine."Unit of Measure Code");
                BodyText += '<br>' + StrSubstNo(LocationLbl, CombinedReceivingLine2."Location Code");
                BodyText += '<br>' + StrSubstNo(LotNoLbl, CombinedReceivingLine2."Lot No.");
                BodyText += '<br>' + StrSubstNo(QualityControlDocNoLbl, LotNoInformation."Certificate Number");
                BodyText += '<br>' + StrSubstNo(ReceivingDateTimeLbl, CurrentDateTime());
                BodyText += '<br>' + StrSubstNo(ReceivedByLbl, UserId());

                Clear(ReceipentList);
                Clear(EmailMessage);
                Clear(Email);

                ReceipentList.Add(SumikaEMailSetup."E-Mail");
                SubjectText := StrSubstNo(SubjectLbl, StrSubstNo(LotNoLbl, CombinedReceivingLine2."Lot No."));
                EmailMessage.Create(ReceipentList, SubjectText, BodyText, true, CCList, BCCList);
                Email.Send(EmailMessage);
            end;
        until SumikaEMailSetup.Next() = 0;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Purch.-Post", OnAfterPurchRcptLineInsert, '', false, false)]
    local procedure "Purch.-Post_OnAfterPurchRcptLineInsert"(PurchaseLine: Record "Purchase Line"; var PurchRcptLine: Record "Purch. Rcpt. Line"; ItemLedgShptEntryNo: Integer; WhseShip: Boolean; WhseReceive: Boolean; CommitIsSupressed: Boolean; PurchInvHeader: Record "Purch. Inv. Header"; var TempTrackingSpecification: Record "Tracking Specification" temporary; PurchRcptHeader: Record "Purch. Rcpt. Header"; TempWhseRcptHeader: Record "Warehouse Receipt Header"; xPurchLine: Record "Purchase Line"; var TempPurchLineGlobal: Record "Purchase Line" temporary)
    var
        PurchaseHeader: Record "Purchase Header";
        CombinedReceivingLine: Record "Combined Receiving Line SMK";
    begin
        if PurchRcptLine.Quantity = 0 then
            exit;

        PurchaseHeader.Get(PurchaseLine."Document Type", PurchaseLine."Document No.");

        CombinedReceivingLine.SetRange("Document No.", PurchaseHeader."Combined Receiving No. SMK");
        CombinedReceivingLine.SetRange("Source Document No.", PurchaseHeader."No.");
        CombinedReceivingLine.SetRange("Source Document Line No.", PurchaseLine."Line No.");
        if not CombinedReceivingLine.FindFirst() then
            exit;

        CombinedReceivingLine."Posted Purchase Receipt No." := PurchRcptHeader."No.";
        CombinedReceivingLine."Posted Purchase Rcpt. Line No." := PurchRcptLine."Line No.";
        CombinedReceivingLine.Modify(true);
    end;

    procedure CreateCombinedReceivingFromPurchaseOrder(var PurchaseHeader: Record "Purchase Header")
    var
        CombinedReceivingHeader: Record "Combined Receiving Header SMK";
    begin
        CombinedReceivingHeader.Init();
        CombinedReceivingHeader.Insert(true);
        CombinedReceivingHeader.Validate("Vendor No.", PurchaseHeader."Buy-from Vendor No.");
        CombinedReceivingHeader.Validate("Purchase Order No.", PurchaseHeader."No.");
        CombinedReceivingHeader.Modify(true);

        PopulateCombinedReceivingLines(CombinedReceivingHeader);

        Page.Run(Page::"Combined Receiving SMK", CombinedReceivingHeader);
    end;

    var
        SumikaSetup: Record "Sumika Setup SMK";
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}