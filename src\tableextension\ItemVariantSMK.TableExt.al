tableextension 60021 "Item Variant SMK" extends "Item Variant"
{
    fields
    {
        field(60000; "Item Description SMK"; Text[100])
        {
            Caption = 'Item Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item.Description where("No." = field("Item No.")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Item Description field.';
        }
    }
}