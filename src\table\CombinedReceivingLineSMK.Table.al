table 60001 "Combined Receiving Line SMK"
{
    Caption = 'Combined Receiving Line';

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(3; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            ToolTip = 'Specifies the value of the Source Document No. field.';
        }
        field(4; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
            ToolTip = 'Specifies the value of the Source Document Line No. field.';
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(6; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(7; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(9; "Quantity Received"; Decimal)
        {
            Caption = 'Quantity Received';
            ToolTip = 'Specifies the value of the Quantity Received field.';
        }
        field(10; "Line Package Count"; Integer)
        {
            Caption = 'Line Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("CombinedReceivingLineDtl SMK" where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Line Package Count field.';
        }
        field(11; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(12; "Qty. to Receive Manual"; Decimal)
        {
            Caption = 'Qty. to Receive Manual';
            ToolTip = 'Specifies the value of the Qty. to Receive Manual field.';
            trigger OnValidate()
            begin
                if SumikaPurchaseManagement.IsPackageTrackingEnabled(Rec."Item No.") then
                    Error(PackageSpecificTrackingEnabledErr);
                // if Rec."Lot No." = '' then
                //     SumikaPurchaseManagement.AssignLotNo(Rec);
            end;
        }
        field(13; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
            Editable = false;
            // trigger OnValidate()
            // var
            //     PurchaseLine: Record "Purchase Line";
            //     LocationCodeErr: Label 'You need to delete created packages before changing Location Code';
            // begin
            //     Rec.CalcFields("Line Package Count");
            //     if Rec."Line Package Count" <> 0 then
            //         Error(LocationCodeErr);

            //     if Rec."Location Code" = xRec."Location Code" then
            //         exit;

            //     PurchaseLine.Get(PurchaseLine."Document Type"::Order, Rec."Source Document No.", Rec."Source Document Line No.");
            //     PurchaseLine.Validate("Location Code", Rec."Location Code");
            //     PurchaseLine.Modify(true);
            // end;
        }
        field(14; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
            ToolTip = 'Specifies the value of the Bin Code field.';
            trigger OnValidate()
            var
                PurchaseLine: Record "Purchase Line";
                BinCodeErr: Label 'You need to delete created packages before changing Bin Code';
            begin
                Rec.CalcFields("Line Package Count");
                if Rec."Line Package Count" <> 0 then
                    Error(BinCodeErr);

                if Rec."Bin Code" = xRec."Bin Code" then
                    exit;

                PurchaseLine.Get(PurchaseLine."Document Type"::Order, Rec."Source Document No.", Rec."Source Document Line No.");
                PurchaseLine.Validate("Bin Code", Rec."Bin Code");
                PurchaseLine.Modify(true);
            end;
        }
        field(15; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(16; "Line Package Quantity"; Decimal)
        {
            Caption = 'Line Package Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("CombinedReceivingLineDtl SMK"."Qty. to Receive" where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Line Package Quantity field.';
        }
        field(17; "Vendor Lot No."; Code[50])
        {
            Caption = 'Vendor Lot No.';
            ToolTip = 'Specifies the value of the Vendor Lot No. field.';
        }

        field(18; "Planned Receipt Date"; Date)
        {
            Caption = 'Planned Receipt Date';
            ToolTip = 'Specifies the value of the Planned Receipt Date field.';
        }
        field(19; "Posted Purchase Receipt No."; Code[20])
        {
            Caption = 'Posted Purchase Receipt No.';
            ToolTip = 'Specifies the value of the Posted Purchase Receipt No. field.';
            AllowInCustomizations = Always;
        }
        field(20; "Posted Purchase Rcpt. Line No."; Integer)
        {
            Caption = 'Posted Purchase Receipt Line No.';
            ToolTip = 'Specifies the value of the Posted Purchase Rcpt. Line No. field.';
            AllowInCustomizations = Always;
        }
        field(21; "Receipt Undone"; Boolean)
        {
            Caption = 'Receipt Undone';
            ToolTip = 'Specifies the value of the Receipt Undone field.';
        }
        field(22; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("Item No.")));
            ToolTip = 'Specifies the packaging type of the item.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; "Source Document No.", "Source Document Line No.", "Item No.")
        {
        }
    }
    trigger OnInsert()
    var
        CombinedReceivingLine: Record "Combined Receiving Line SMK";
    begin
        CombinedReceivingLine.SetRange("Document No.", Rec."Document No.");
        if CombinedReceivingLine.FindLast() then
            Rec."Line No." := CombinedReceivingLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        PackageSpecificTrackingEnabledErr: Label 'You can not enter Qty. to Receive manually for an item with package specific tracking enabled.';
}
