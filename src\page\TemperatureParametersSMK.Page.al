page 60007 "Temperature Parameters SMK"
{
    ApplicationArea = All;
    Caption = 'Temperature Parameters';
    PageType = List;
    SourceTable = "Temperature Parameter SMK";
    UsageCategory = Administration;
    DelayedInsert = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Machine No."; Rec."Machine No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Barrel No."; Rec."Barrel No.")
                {
                }
                field(Temperature; Rec."Temperature (°C)")
                {
                }
            }
        }
    }
}