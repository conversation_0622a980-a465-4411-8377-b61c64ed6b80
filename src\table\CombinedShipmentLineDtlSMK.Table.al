table 60019 "CombinedShipmentLineDtl SMK"
{
    Caption = 'Combined Shipment Line Detail';
    DrillDownPageId = "Combined Shipment Line Dtl SMK";
    LookupPageId = "Combined Shipment Line Dtl SMK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(4; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            AllowInCustomizations = Always;
        }
        field(5; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
            AllowInCustomizations = Always;
        }
        field(6; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(7; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(8; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(9; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(10; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(13; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            AllowInCustomizations = Always;
        }
        // field(15; "Buy-From Vendor No."; Code[20])
        // {
        //     Caption = 'Buy-From Vendor No.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Combined Shipment Header SMK"."Vendor No." where("No." = field("Document No.")));
        // }
        // field(16; "Buy-from Vendor Name"; Text[100])
        // {
        //     Caption = 'Buy-from Vendor Name';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Combined Shipment Header SMK"."Vendor Name" where("No." = field("Document No.")));
        // }
        field(17; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(18; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            ToolTip = 'Specifies the value of the Bin Code field.';
        }
        field(19; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(20; "Parent Package No."; Code[50])
        {
            Caption = 'Parent Package No.';
            ToolTip = 'Specifies the value of the Parent Package No. field.';
        }
        field(21; "Ready to Ship"; Boolean)
        {
            Caption = 'Ready to Ship';
            ToolTip = 'Specifies the value of the Ready to Ship field.';
        }
        field(22; "Old Bin Code"; Code[20])
        {
            Caption = 'Old Bin Code';
            ToolTip = 'Specifies the value of the Old Bin Code field.';
        }

    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
            SumIndexFields = Quantity;
        }
    }
    trigger OnInsert()
    var
        CombinedShipmentLineDetail: Record "CombinedShipmentLineDtl SMK";
    begin
        CombinedShipmentLineDetail.SetRange("Document No.", Rec."Document No.");
        CombinedShipmentLineDetail.SetRange("Document Line No.", Rec."Document Line No.");
        if CombinedShipmentLineDetail.FindLast() then
            Rec."Line No." := CombinedShipmentLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}