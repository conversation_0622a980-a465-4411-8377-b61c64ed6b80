﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="ItemNo">
          <DataField>ItemNo</DataField>
        </Field>
        <Field Name="Description2">
          <DataField>Description2</DataField>
        </Field>
        <Field Name="VariantCode">
          <DataField>VariantCode</DataField>
        </Field>
        <Field Name="PackageNo">
          <DataField>PackageNo</DataField>
        </Field>
        <Field Name="MachineScaleNo">
          <DataField>MachineScaleNo</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="LotNoSMK">
          <DataField>LotNoSMK</DataField>
        </Field>
        <Field Name="VendorLotNoSMK">
          <DataField>VendorLotNoSMK</DataField>
        </Field>
        <Field Name="ExpirationDateSMK">
          <DataField>ExpirationDateSMK</DataField>
        </Field>
        <Field Name="ApprovalDateSMK">
          <DataField>ApprovalDateSMK</DataField>
        </Field>
        <Field Name="ShelfLifeSMK">
          <DataField>ShelfLifeSMK</DataField>
        </Field>
        <Field Name="UnitOfMeasureCode">
          <DataField>UnitOfMeasureCode</DataField>
        </Field>
        <Field Name="Inventory">
          <DataField>Inventory</DataField>
        </Field>
        <Field Name="InventoryFormat">
          <DataField>InventoryFormat</DataField>
        </Field>
        <Field Name="BarCode">
          <DataField>BarCode</DataField>
        </Field>
        <Field Name="ParentPackageNoSMK_PackageNoInformation">
          <DataField>ParentPackageNoSMK_PackageNoInformation</DataField>
        </Field>
        <Field Name="Package_Order_No__SMK">
          <DataField>Package_Order_No__SMK</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>