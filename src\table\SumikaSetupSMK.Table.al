table 60003 "Sumika Setup SMK"
{
    Caption = 'Sumika Setup';

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; "Combined Receiving No. Series"; Code[20])
        {
            Caption = 'Combined Receiving No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Combined Receiving No. Series field.';
        }
        field(3; "Package Transfer No. Series"; Code[20])
        {
            Caption = 'Package Transfer No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Package Transfer No. Series field.';
        }
        field(4; "Quality Control No. Series"; Code[20])
        {
            Caption = 'Quality Control No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Quality Control No. Series field.';
        }
        field(5; "Package Transfer Template Name"; Code[10])
        {
            Caption = 'Package Transfer Template Name';
            TableRelation = "Item Journal Template";
            ToolTip = 'Specifies the value of the Package Transfer Template Name field.';
        }
        field(6; "Package Transfer Batch Name"; Code[10])
        {
            Caption = 'Package Transfer Template Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Transfer Template Name"));
            ToolTip = 'Specifies the value of the Package Transfer Template Name field.';
        }
        field(7; "Output Journal Template Name"; Code[10])
        {
            Caption = 'Output Journal Template Name';
            TableRelation = "Item Journal Template";
            ToolTip = 'Specifies the value of the Output Journal Template Name field.';
        }
        field(8; "Output Journal Batch Name"; Code[10])
        {
            Caption = 'Output Journal Template Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Output Journal Template Name"));
            ToolTip = 'Specifies the value of the Output Journal Template Name field.';
        }
        field(9; "Combined Shipment No. Series"; Code[20])
        {
            Caption = 'Combined Shipment No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Combined Shipment No. Series field.';
        }
        field(14; "Parent Package No. Series"; Code[20])
        {
            Caption = 'Parent Package No. Series';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the value of the Parent Package No. Series field.';
        }
        field(15; "Package Split No. Series"; Code[20])
        {
            Caption = 'Package Split No. Series';
            TableRelation = "No. Series".Code;
            ToolTip = 'Specifies the value of the Package Split No. Series field.';
        }
        field(16; "Package Split Jnl. Tmpl. Name"; Code[10])
        {
            Caption = 'Package Split Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Transfer));
            ToolTip = 'Specifies the value of the Package Split Journal Template Name field.';
        }
        field(17; "Package Split Jnl. Batch Name"; Code[10])
        {
            Caption = 'Package Split Journal Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Package Split Jnl. Tmpl. Name"));
            ToolTip = 'Specifies the value of the Package Split Journal Batch Name field.';
        }
        field(18; "Consumption Jnl. Template Name"; Code[10])
        {
            Caption = 'Consumption Journal Template Name';
            TableRelation = "Item Journal Template" where(Type = const(Consumption));
            ToolTip = 'Specifies the value of the Consumption Journal Template Name field.';
        }
        field(19; "Consumption Jnl. Batch Name"; Code[10])
        {
            Caption = 'Consumption Journal Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Consumption Jnl. Template Name"));
            ToolTip = 'Specifies the value of the Consumption Journal Batch Name field.';
        }
        field(20; "Print Label Quantity"; Boolean)
        {
            Caption = 'Print Label Quantity';
            ToolTip = 'Specifies the value of the Print Label Quantity field.';
        }
        field(21; "Transfer&Ship Comb. Shipment"; Boolean)
        {
            Caption = 'Transfer & Ship Combined Shipment';
            ToolTip = 'Specifies the value of the Transfer & Ship Combined Shipment field.';
        }
        field(22; "Temp. Quality Control Nos"; Code[20])
        {
            Caption = 'Temp. Quality Control Nos';
            ToolTip = 'Specifies the value of the Temp. Quality Control Nos field.';
            TableRelation = "No. Series".Code;
        }
    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    var
        RecordHasBeenRead: Boolean;

    procedure GetRecordOnce()
    begin
        if RecordHasBeenRead then
            exit;
        Get();
        RecordHasBeenRead := true;
    end;

    procedure InsertIfNotExists()
    begin
        Reset();
        if not Get() then begin
            Init();
            Insert(true);
        end;
    end;
}