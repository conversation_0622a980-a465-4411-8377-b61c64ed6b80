page 60016 "Enter Test Result Dialog SMK"
{
    ApplicationArea = All;
    Caption = 'Enter Test Result Dialog';
    PageType = StandardDialog;
    SourceTable = "Quality Control Line Dtl. SMK";

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("Package No."; Rec."Package No.")
                {
                    trigger OnLookup(var Text: Text): Boolean
                    begin
                        SumikaProductionMgt.OnAfterLookupPackageNo_EnterResultPage(Rec);
                        CurrPage.Update();
                    end;

                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;
                }
                field("Package Comment Count"; Rec."Package Comment Count")
                {
                    Editable = false;
                    trigger OnDrillDown()
                    var
                        ItemTrackingComment: Record "Item Tracking Comment";
                        ItemTrackingComments: Page "Item Tracking Comments";
                    begin
                        ItemTrackingComment.SetRange("Item No.", Rec."Item No.");
                        ItemTrackingComment.SetRange("Variant Code", Rec."Variant Code");
                        ItemTrackingComment.SetRange("Serial/Lot No.", Rec."Package No.");
                        ItemTrackingComments.SetTableView(ItemTrackingComment);
                        ItemTrackingComments.RunModal();
                    end;
                }
                field("QC Control Quantity"; Rec."QC Control Quantity")
                {
                }
                field("Result Value"; Rec."Result Value")
                {
                    Enabled = Rec."Specification Reference" <> Rec."Specification Reference"::Selection;
                }
                field("Selection Result Value"; Rec."Selection Result Value")
                {
                    Enabled = Rec."Specification Reference" = Rec."Specification Reference"::Selection;
                }
            }
            group(TestInformation)
            {
                Caption = 'Test Information';
                Editable = false;
                field("Specification Code"; Rec."Specification Code")
                {
                }
                field("Specification Description"; Rec."Specification Description")
                {
                }
                field("Specification Reference"; Rec."Specification Reference")
                {
                }
                field("Min Value"; Rec."Min Value")
                {
                }
                field("Max Value"; Rec."Max Value")
                {
                }
                field("Exact Value"; Rec."Exact Value")
                {
                }
                field("Selection Value"; Rec."Selection Value")
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field(Standard; Rec.Standard)
                {
                }
                field("Created By"; Rec."Created By")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }

    // actions
    // {
    //     area(Processing)
    //     {
    //         action(PackageComments)
    //         {
    //             ApplicationArea = All;
    //             Caption = 'Package Comments';
    //             Image = Comment;
    //             Promoted = true;
    //             PromotedCategory = Process;
    //             PromotedIsBig = true;
    //             ToolTip = 'View or add comments for the selected package.';

    //             trigger OnAction()
    //             var
    //                 PackageComment: Record "Package Comment SMK";
    //                 PackageCommentsPage: Page "Package Comments SMK";
    //             begin
    //                 if Rec."Package No." = '' then
    //                     Error('Please select a package first.');

    //                 PackageComment.SetRange("Package No.", Rec."Package No.");
    //                 PackageCommentsPage.SetTableView(PackageComment);
    //                 PackageCommentsPage.RunModal();
    //             end;
    //         }
    //     }
    // }

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        QualityControlLine: Record "Quality Control Line SMK";
    begin
        if CloseAction = CloseAction::Cancel then
            Rec.Delete(true)
        else begin
            QualityControlLine.Get(Rec."Document No.", Rec."Document Line No.");
            SumikaQualityCtrlMgt.CalculateQualityControlLineDetailStatus(Rec);
            SumikaQualityCtrlMgt.CalculateQualityControlLineStatus(QualityControlLine);
        end;
    end;

    var
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}