page 60024 "Combined Shipment List SMK"
{
    ApplicationArea = All;
    Caption = 'Combined Shipments';
    PageType = List;
    SourceTable = "Combined Shipment Header SMK";
    UsageCategory = Lists;
    CardPageId = "Combined Shipment SMK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field("Total Package Quantity"; Rec."Total Package Count")
                {
                }
                field(Shipped; Rec."Transferred to Sales Order")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}