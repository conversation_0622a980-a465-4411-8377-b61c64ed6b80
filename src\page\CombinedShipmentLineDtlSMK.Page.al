page 60025 "Combined Shipment Line Dtl SMK"
{
    ApplicationArea = All;
    Caption = 'Combined Shipment Line Details';
    PageType = List;
    SourceTable = "CombinedShipmentLineDtl SMK";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Bin Code"; Rec."Bin Code")
                {
                }
                field("Old Bin Code"; Rec."Old Bin Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Parent Package No."; Rec."Parent Package No.")
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field("Ready to Ship"; Rec."Ready to Ship")
                {
                }
            }
        }
    }
}