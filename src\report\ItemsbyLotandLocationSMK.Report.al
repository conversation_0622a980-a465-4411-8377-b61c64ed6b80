report 60004 "Items by Lot and Location SMK"
{
    ApplicationArea = All;
    Caption = 'Items by Lot and Location';
    UsageCategory = ReportsAndAnalysis;
    // requestpage
    // {
    //     layout
    //     {
    //         area(Content)
    //         {
    //             field(ViewCostAmounts; ViewCostAmounts)
    //             {
    //                 ApplicationArea = All;
    //             }
    //         }

    //     }
    // }
    dataset
    {
        dataitem(ItemLedgerEntry; "Item Ledger Entry")
        {
            //RequestFilterFields = "Date Filter SMK";
            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(Description; Description)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(ItemDescription; SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode(ItemLedgerEntry."Item No.", ''))
            {
            }
            column(PackagingTypeCode; SumikaBasicFunctions.GetPackagingTypeCodeFromItemNo(ItemLedgerEntry."Item No."))
            {
            }
            dataitem("Quality Control Header SMK"; "Quality Control Header SMK")
            {
                DataItemLink = "Lot No." = field("Lot No.");
                column(Status; Status)
                {
                }
            }
        }
    }

    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";

}