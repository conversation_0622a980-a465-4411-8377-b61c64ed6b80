pageextension 60008 "Released Production Order SMK" extends "Released Production Order"
{
    layout
    {
        addafter("Source No.")
        {
            field("Packaging Type SMK"; Rec."Packaging Type SMK")
            {
                ApplicationArea = All;
            }
            field("Routing No. SMK"; Rec."Routing No.")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the routing number used for this production order.';
            }
        }
        addafter(ProdOrderLines)
        {
            group("ProductionParameters SMK")
            {
                Caption = 'Production Parameters';
                field("Type SMK"; Rec."Type SMK")
                {
                    ApplicationArea = All;
                }
                field("Machine No. SMK"; Rec."Machine No. SMK")
                {
                    ApplicationArea = All;
                }
                field("Machine Scale No. SMK"; Rec."Machine Scale No. SMK")
                {
                    ApplicationArea = All;
                }
                field("Barrel Temp. (°C) SMK"; Rec."Barrel Temp. (°C) SMK")
                {
                    ApplicationArea = All;
                }
                field("Bath Temperature SMK"; Rec."Bath Temperature (°C) SMK")
                {
                    ApplicationArea = All;
                }
                field("Filter Diameter (Mesh) SMK"; Rec."Filter Diameter (Mesh) SMK")
                {
                    ApplicationArea = All;
                }
                field("Output (kg/saat) SMK"; Rec."Output (kg/saat) SMK")
                {
                    ApplicationArea = All;
                }
                field("Screw Speed (rpm) SMK"; Rec."Screw Speed (rpm) SMK")
                {
                    ApplicationArea = All;
                }
                field("Torque (%) SMK"; Rec."Torque (%) SMK")
                {
                    ApplicationArea = All;
                }
                field("Vaccuum (bar) SMK"; Rec."Vaccuum (bar) SMK")
                {
                    ApplicationArea = All;
                }
                field("Pelletization Speed (m/min) SMK"; Rec."Pellet. Speed (m/min) SMK")
                {
                    ApplicationArea = All;
                    BlankZero = true;
                }
                field("Pellet Temperature (°C) SMK"; Rec."Pellet Temperature (°C) SMK")
                {
                    ApplicationArea = All;
                    BlankZero = true;
                }
            }
        }
    }
    actions
    {
        addfirst("&Print")
        {
            action("ProductionOrderReport SMK")
            {
                ApplicationArea = All;
                Caption = 'Production Order Report';
                Promoted = true;
                PromotedCategory = Report;
                PromotedIsBig = true;
                Image = Print;
                ToolTip = 'Executes the Production Order Report action.';
                trigger OnAction()
                var
                    ProdOrderLine: Record "Prod. Order Line";
                begin
                    ProdOrderLine.SetRange(Status, Rec.Status);
                    ProdOrderLine.SetRange("Prod. Order No.", Rec."No.");
                    ProdOrderLine.FindFirst();
                    SumikaProductionMgt.AssignLotNo(ProdOrderLine);

                    Rec.SetRecFilter();
                    Commit();//Commit the record to get the latest data
                    Report.Run(Report::"Production Order Report SMK", true, true, Rec);
                end;
            }
            action("TransferredComponents SMK")
            {
                ApplicationArea = All;
                Caption = 'Transferred Components';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Components;
                ToolTip = 'Executes the Transferred Components action.';
                PromotedOnly = true;
                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    PackageNoInformation.SetRange("Production Order No. SMK", Rec."No.");
                    Page.Run(Page::"Package No. Information List", PackageNoInformation)
                end;
            }
        }
    }
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}