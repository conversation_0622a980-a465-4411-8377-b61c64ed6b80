# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Sumika Customizations** is a Business Central AL extension for Sumika Polymer Compounds, developed by Infotek Yazilim ve Donanim A.S. The extension provides specialized functionality for manufacturing, quality control, packaging, and inventory management in the polymer compounds industry.

- **App ID**: af0d0853-082a-47db-b27b-5ed87bdb9a6b
- **Version**: 26.0.0.0
- **Runtime**: 15.2
- **Object ID Range**: 60000-60999
- **Suffix Convention**: All custom objects use "SMK" suffix

## Development Environment

### Launch Configuration
- **Primary Environment**: SUMIKA_DEV3 (Sandbox)
- **Tenant**: 851f6719-46a2-43ac-8f03-4a84fc1fbffc
- **Startup Page**: 60024
- **Debug Features**: SQL Information Debugger enabled, Long Running SQL Statements enabled

### Build and Development Commands
```bash
# AL extension compilation is handled through VS Code AL extension
# No npm/node.js build process - this is a pure AL project
```

### Code Analysis
- **Ruleset**: Uses `infotek.ruleset.json` with custom company rules
- **Code Analyzers**: CodeCop, UICop, PerTenantExtensionCop, BusinessCentral.LinterCop.dll
- **Code Actions**: Automatic reorganization on save via CRS extension

## Architecture and Code Structure

### Core Business Areas
1. **Manufacturing Management** - Production orders, BOM handling, consumption tracking
2. **Quality Control System** - Test specifications, quality control documents, test results
3. **Package Management** - Package creation, splitting, transfers, barrel handling
4. **Inventory Extensions** - Enhanced item tracking, lot management, location handling
5. **Integration APIs** - Item Ledger Entry API and data synchronization

### Key Custom Tables
- **Sumika Setup SMK** (60003) - Central configuration table
- **Quality Control Header/Lines** (60011/60012) - QC document management  
- **Package Transfer Header/Lines** (60018/60019) - Package movement tracking
- **Item Quality Control Spec** (60010) - Item-specific QC specifications
- **Production Parameter** (60025) - Manufacturing parameter templates

### Extension Pattern
All standard BC tables are extended with SMK suffix following consistent patterns:
- Item → Item SMK (fields: Packaging Type, Automatic Consumption, Include In Mail)
- Production Order → Production Order SMK 
- Item Ledger Entry → Item Ledger Entry SMK (Cost Amount by Date field)
- Sales/Purchase documents with packaging and QC integration

### Naming Conventions
- **Objects**: Pascal case + " SMK" suffix (e.g., "Quality Control SMK")
- **Variables**: Consistent with AL guidelines
- **Fields**: Custom fields start at 60000+ range
- **Affixes**: " INF" and " SMK" are mandatory patterns (enforced by alOutline settings)

## Performance Considerations

### Critical Performance Patterns
Based on PERFORMANCE_FIX_SUMMARY.md lessons learned:

1. **Always use SetLoadFields()** for reports accessing large tables
2. **Avoid nested queries** in report column expressions - use proper joins instead
3. **Limit column retrieval** - only fetch necessary fields from large tables like Item Ledger Entry
4. **Remove debug code** (Message() calls) before production deployment
5. **Test with realistic data volumes** before deploying changes

### Example Performance Optimization:
```al
// Good - Use SetLoadFields to limit data retrieval
SetLoadFields("Item No.", "Variant Code", Description, "Location Code", "Lot No.", 
             Quantity, "Unit of Measure Code", "Posting Date", "Date Filter SMK");
```

## Translation Support
- **Target Language**: Turkish (TRK)
- **Translation Files**: Supported via TranslationFile feature
- **Snippet Language**: Turkish configured in settings

## Key Integration Points
- **Package No. Information** table integration for inventory tracking
- **Item Tracking Comment** extensions for enhanced traceability  
- **Production BOM** extensions for manufacturing specifications
- **Email Setup** table for notification configurations

## Development Guidelines
- Follow the established SMK suffix pattern for all new objects
- Use the company ruleset (infotek.ruleset.json) for code quality
- Custom fields should start from 60000+ range to avoid conflicts
- Maintain consistency with existing architectural patterns
- Always consider performance impact when working with Item Ledger Entry or similar large tables

## Code Quality and Linting Standards

### Critical Linting Rules (Must Follow)
Based on `infotek.ruleset.json` and common issues:

#### LC0020 - ApplicationArea Redundancy
- **Remove redundant ApplicationArea** declarations from fields/actions when page already defines `ApplicationArea = All`
- Page-level ApplicationArea applies to all child elements automatically

#### LC0036 - ToolTip Standards
- **All ToolTips must start with "Specifies"** - never use "Shows", "Enter", "Select", etc.
- Example: `ToolTip = 'Specifies the production order for consumption calculation.';`

#### LC0048 - Error Handling Best Practices
- **Never use direct Error() calls** - always use Label variables for better telemetry
- **Always include Turkish translations** in Label comments using TRK format
- **Example Pattern**:
  ```al
  var
      ProductionOrderRequiredErr: Label 'Production order must be selected.', 
                                  Comment = 'TRK="Üretim emri seçilmelidir."';
  
  // Usage
  if ProductionOrderNo = '' then
      Error(ProductionOrderRequiredErr);
  ```

#### Other Important Rules
- **LC0072**: Variable names should be suffixed with type (disabled but recommended)
- **LC0084**: Always handle Record.Get() boolean return values
- **LC0091**: Translate all labels for Turkish language support

### Pre-commit Checklist
Before committing AL code changes:
1. Ensure all ToolTips start with "Specifies"
2. Replace any direct Error() calls with Label variables
3. Remove redundant ApplicationArea declarations
4. Add Turkish translations to all new Label variables
5. Test with realistic data volumes for performance-critical changes