pageextension 60024 "General Journal SMK" extends "General Journal"
{
    layout
    {
        addafter("Posting Date")
        {
            field("Due Date SMK"; Rec."Due Date")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the due date on the entry.';
            }
        }
    }
    actions
    {
        addfirst("F&unctions")
        {
            action("NewGLAccountLine SMK")
            {
                ApplicationArea = All;
                Caption = 'New G/L Account Line';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = GL;
                ToolTip = 'Executes the New G/L Account Line action.';

                trigger OnAction()
                var
                    NewGLAccountLine: Page "New G/L Account Line SMK";
                begin
                    NewGLAccountLine.GetTemplateAndBatchName(Rec."Journal Template Name", Rec."Journal Batch Name");

                    NewGLAccountLine.RunModal();
                end;
            }
        }
    }
}