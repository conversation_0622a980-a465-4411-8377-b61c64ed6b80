pageextension 60022 "Item Tracking Comments SMK" extends "Item Tracking Comments"
{
    layout
    {
        addlast(Control1)
        {
            field("Created By SMK"; Rec."Created By SMK")
            {
                ApplicationArea = All;
            }
        }
        modify(Date)
        {
            Editable = false;
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        Rec.Date := WorkDate();
    end;
}