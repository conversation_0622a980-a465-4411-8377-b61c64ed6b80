pageextension 60012 "Lot No. Information List SMK" extends "Lot No. Information List"
{
    layout
    {
        addlast(Control1)
        {
            field("Vendor Lot No. SMK"; Rec."Vendor Lot No. SMK")
            {
                ApplicationArea = All;
            }
            field("Expiration Date SMK"; SumikaBasicFunctions.GetExpirationDateFromLotNo(Rec."Lot No."))
            {
                Caption = 'Expiration Date';
                ToolTip = 'Specifies the value of the Expiration Date field.';
                ApplicationArea = All;
            }
            field("Packaging Type Code SMK"; Rec."Packaging Type Code SMK")
            {
                ApplicationArea = All;
            }
            field("Company No. SMK"; Rec."Company No. SMK")
            {
                ApplicationArea = All;
            }
            field("Company Name SMK"; Rec."Company Name SMK")
            {
                ApplicationArea = All;
            }
            field("SystemCreatedAt SMK"; Rec.SystemCreatedAt)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the SystemCreatedAt field.';
            }
        }
        modify("Expired Inventory")
        {
            Visible = false;
        }
    }
    var
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
}