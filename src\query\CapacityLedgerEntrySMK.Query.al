query 60000 "Capacity Ledger Entry SMK"
{
    Caption = 'Capacity Ledger Entry';
    QueryType = Normal;
    DataAccessIntent = ReadOnly;

    elements
    {
        dataitem(CapacityLedgerEntry; "Capacity Ledger Entry")
        {
            column(EntryNo; "Entry No.")
            {
            }
            column(No; "No.")
            {
            }
            column(PostingDate; "Posting Date")
            {
            }
            column("Type"; "Type")
            {
            }
            column(DocumentNo; "Document No.")
            {
            }
            column(Description; Description)
            {
            }
            column(OperationNo; "Operation No.")
            {
            }
            column(WorkCenterNo; "Work Center No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(SetupTime; "Setup Time")
            {
            }
            column(RunTime; "Run Time")
            {
            }
            column(StopTime; "Stop Time")
            {
            }
            column(InvoicedQuantity; "Invoiced Quantity")
            {
            }
            column(OutputQuantity; "Output Quantity")
            {
            }
            column(ScrapQuantity; "Scrap Quantity")
            {
            }
            column(ConcurrentCapacity; "Concurrent Capacity")
            {
            }
            column(CapUnitofMeasureCode; "Cap. Unit of Measure Code")
            {
            }
            column(QtyperCapUnitofMeasure; "Qty. per Cap. Unit of Measure")
            {
            }
            column(GlobalDimension1Code; "Global Dimension 1 Code")
            {
            }
            column(GlobalDimension2Code; "Global Dimension 2 Code")
            {
            }
            column(LastOutputLine; "Last Output Line")
            {
            }
            column(CompletelyInvoiced; "Completely Invoiced")
            {
            }
            column(StartingTime; "Starting Time")
            {
            }
            column(EndingTime; "Ending Time")
            {
            }
            column(RoutingNo; "Routing No.")
            {
            }
            column(RoutingReferenceNo; "Routing Reference No.")
            {
            }
            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(QtyperUnitofMeasure; "Qty. per Unit of Measure")
            {
            }
            column(DocumentDate; "Document Date")
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(StopCode; "Stop Code")
            {
            }
            column(ScrapCode; "Scrap Code")
            {
            }
            column(WorkCenterGroupCode; "Work Center Group Code")
            {
            }
            column(WorkShiftCode; "Work Shift Code")
            {
            }
            column(DirectCost; "Direct Cost")
            {
            }
            column(OverheadCost; "Overhead Cost")
            {
            }
            column(DirectCostACY; "Direct Cost (ACY)")
            {
            }
            column(OverheadCostACY; "Overhead Cost (ACY)")
            {
            }
            column(Subcontracting; Subcontracting)
            {
            }
            column(OrderType; "Order Type")
            {
            }
            column(OrderNo; "Order No.")
            {
            }
            column(OrderLineNo; "Order Line No.")
            {
            }
            column(DimensionSetID; "Dimension Set ID")
            {
            }
            column(ShortcutDimension3Code; "Shortcut Dimension 3 Code")
            {
            }
            column(ShortcutDimension4Code; "Shortcut Dimension 4 Code")
            {
            }
            column(ShortcutDimension5Code; "Shortcut Dimension 5 Code")
            {
            }
            column(ShortcutDimension6Code; "Shortcut Dimension 6 Code")
            {
            }
            column(ShortcutDimension7Code; "Shortcut Dimension 7 Code")
            {
            }
            column(ShortcutDimension8Code; "Shortcut Dimension 8 Code")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}