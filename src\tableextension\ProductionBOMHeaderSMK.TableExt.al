tableextension 60026 "Production BOM Header SMK" extends "Production BOM Header"
{
    fields
    {
        field(60000; "Total Weight (KG) SMK"; Decimal)
        {
            Caption = 'Total Weight (KG)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Production BOM Line"."Quantity per" where("Production BOM No." = field("No."), "Version Code" = field("Version Nos."), "Unit of Measure Code" = const('KG')));
            ToolTip = 'Specifies the value of the Total Weight (KG) field.';
        }

    }
}