{"id": "table.entry", "title": "+ New Entry Table", "description": "Create a new Entry Table", "position": "2", "body": ["table ${1:id} ${2:MyTable}", "{", "\tDataClassification = ${3|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t", "\tfields", "\t{", "\t\tfield(1;\"Entry No.\"; integer)", "\t\t{", "\t\t\tCaption = 'Entry No.';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\t", "\t\tfield(2;\"Document No.\"; Code[20])", "\t\t{", "\t\t\tCaption = 'Document No.';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\t", "\t\tfield(3;Description; Text[50])", "\t\t{", "\t\t\tCaption = 'Description';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\tfield(4;\"Quantity\"; decimal)", "\t\t{", "\t\t\tCaption = 'Quantity';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\t", "\t\tfield(5;\"Amount\"; decimal)", "\t\t{", "\t\t\tCaption = 'Amount';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\t", "\t\tfield(6;\"User ID\"; Code[50])", "\t\t{", "\t\t\tCaption = 'User ID';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\t", "\t\tfield(480;\"Dimension Set ID\"; integer)", "\t\t{", "\t\t\tCaption = 'Dimension Set ID';", "\t\t\tDataClassification = ${4|ToBeClassified,CustomerContent,EndUserIdentifiableInformation,AccountData,EndUserPseudonymousIdentifiers,OrganizationIdentifiableInformation,SystemMetadata|};", "\t\t}", "\t\t", "\t\t$5", "\t}", "\t", "\tkeys", "\t{", "\t\tkey(${6:PK}; \"Entry No.\")", "\t\t{", "\t\t\tClustered = ${7|true,false|};", "\t\t}", "\t}", "\t", "}"]}