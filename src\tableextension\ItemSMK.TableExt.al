tableextension 60017 "Item SMK" extends Item
{
    fields
    {
        field(60000; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            TableRelation = "Packaging Type SMK".Code;
            ToolTip = 'Specifies the value of the Packaging Type field.';
        }
        field(60001; "Automatic Consumption SMK"; Boolean)
        {
            Caption = 'Automatic Consumption';
            ToolTip = 'Specifies the value of the Automatic Consumption field.';
        }
        field(60002; "Include In Mail SMK"; Boolean)
        {
            Caption = 'Include In Mail';
            InitValue = true;
            ToolTip = 'Specifies the value of the Include In Mail field.';
        }
    }
    keys
    {
        key(SK; "Packaging Type SMK")
        {
        }
    }
    fieldgroups
    {
        addlast(DropDown; "No. 2", "Packaging Type SMK")
        {

        }
    }
}