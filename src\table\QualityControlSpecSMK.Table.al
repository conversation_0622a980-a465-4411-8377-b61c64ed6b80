table 60009 "Quality Control Spec. SMK"
{
    Caption = 'Quality Control Specification';
    DataClassification = CustomerContent;
    LookupPageId = "Quality Control Specs. SMK";
    DrillDownPageId = "Quality Control Specs. SMK";

    fields
    {
        field(1; Code; Code[10])
        {
            Caption = 'Code';
            NotBlank = true;
            ToolTip = 'Specifies the value of the Code field.';
        }
        field(2; Description; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the value of the Description field.';
        }
        field(3; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            TableRelation = "Unit of Measure".Code;
            ValidateTableRelation = false;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(4; Standard; Text[30])
        {
            Caption = 'Standard';
            ToolTip = 'Specifies the value of the Standard field.';
        }
    }
    keys
    {
        key(PK; Code)
        {
            Clustered = true;
        }
    }
}