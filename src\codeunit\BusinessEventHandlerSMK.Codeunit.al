codeunit 60011 "Business Event Handler SMK"
{
    [ExternalBusinessEvent('OnAfterPostSalesHeaderBe', 'On After Post Sales Header Be', 'Triggered when After Post Sales Header', EventCategory::"Infotek Events")]
    internal procedure OnAfterPostSalesHeaderBe(LastShippingNo: Code[20]; PostedDocumentNo: Code[20]; LocationCode: Code[10]; CustomerNo: Code[20]; CustomerName: Text[100]; ExternalDocumentNo: Code[35])
    var
    begin
    end;
}