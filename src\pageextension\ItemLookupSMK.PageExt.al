pageextension 60023 "Item Lookup SMK" extends "Item Lookup"
{
    layout
    {
        addafter("No.")
        {
            field("No. 2 SMK"; Rec."No. 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the No. 2 field.';
            }
            field("Packaging Type SMK"; Rec."Packaging Type SMK")
            {
                ApplicationArea = All;
            }
        }
        addafter(Description)
        {
            field("Description 2 SMK"; Rec."Description 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies information in addition to the description.';
            }
        }
    }
}