query 60001 "Value Entry SMK"
{
    Caption = 'Value Entry';
    QueryType = Normal;
    DataAccessIntent = ReadOnly;

    elements
    {
        dataitem(ValueEntry; "Value Entry")
        {
            column(EntryNo; "Entry No.")
            {
            }
            column(ItemNo; "Item No.")
            {
            }
            column(PostingDate; "Posting Date")
            {
            }
            column(ItemLedgerEntryType; "Item Ledger Entry Type")
            {
            }
            column(SourceNo; "Source No.")
            {
            }
            column(DocumentNo; "Document No.")
            {
            }
            column(Description; Description)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(InventoryPostingGroup; "Inventory Posting Group")
            {
            }
            column(SourcePostingGroup; "Source Posting Group")
            {
            }
            column(ItemLedgerEntryNo; "Item Ledger Entry No.")
            {
            }
            column(ValuedQuantity; "Valued Quantity")
            {
            }
            column(ItemLedgerEntryQuantity; "Item Ledger Entry Quantity")
            {
            }
            column(InvoicedQuantity; "Invoiced Quantity")
            {
            }
            column(CostperUnit; "Cost per Unit")
            {
            }
            column(SalesAmountActual; "Sales Amount (Actual)")
            {
            }
            column(SalespersPurchCode; "Salespers./Purch. Code")
            {
            }
            column(DiscountAmount; "Discount Amount")
            {
            }
            column(UserID; "User ID")
            {
            }
            column(SourceCode; "Source Code")
            {
            }
            column(AppliestoEntry; "Applies-to Entry")
            {
            }
            column(GlobalDimension1Code; "Global Dimension 1 Code")
            {
            }
            column(GlobalDimension2Code; "Global Dimension 2 Code")
            {
            }
            column(SourceType; "Source Type")
            {
            }
            column(CostAmountActual; "Cost Amount (Actual)")
            {
            }
            column(CostPostedtoGL; "Cost Posted to G/L")
            {
            }
            column(ReasonCode; "Reason Code")
            {
            }
            column(DropShipment; "Drop Shipment")
            {
            }
            column(JournalBatchName; "Journal Batch Name")
            {
            }
            column(GenBusPostingGroup; "Gen. Bus. Posting Group")
            {
            }
            column(GenProdPostingGroup; "Gen. Prod. Posting Group")
            {
            }
            column(DocumentDate; "Document Date")
            {
            }
            column(ExternalDocumentNo; "External Document No.")
            {
            }
            column(CostAmountActualACY; "Cost Amount (Actual) (ACY)")
            {
            }
            column(CostPostedtoGLACY; "Cost Posted to G/L (ACY)")
            {
            }
            column(CostperUnitACY; "Cost per Unit (ACY)")
            {
            }
            column(DocumentType; "Document Type")
            {
            }
            column(DocumentLineNo; "Document Line No.")
            {
            }
            column(VATReportingDate; "VAT Reporting Date")
            {
            }
            column(OrderType; "Order Type")
            {
            }
            column(OrderNo; "Order No.")
            {
            }
            column(OrderLineNo; "Order Line No.")
            {
            }
            column(ExpectedCost; "Expected Cost")
            {
            }
            column(ItemChargeNo; "Item Charge No.")
            {
            }
            column(ValuedByAverageCost; "Valued By Average Cost")
            {
            }
            column(PartialRevaluation; "Partial Revaluation")
            {
            }
            column(Inventoriable; Inventoriable)
            {
            }
            column(ValuationDate; "Valuation Date")
            {
            }
            column(EntryType; "Entry Type")
            {
            }
            column(VarianceType; "Variance Type")
            {
            }
            column(PurchaseAmountActual; "Purchase Amount (Actual)")
            {
            }
            column(PurchaseAmountExpected; "Purchase Amount (Expected)")
            {
            }
            column(SalesAmountExpected; "Sales Amount (Expected)")
            {
            }
            column(CostAmountExpected; "Cost Amount (Expected)")
            {
            }
            column(CostAmountNonInvtbl; "Cost Amount (Non-Invtbl.)")
            {
            }
            column(CostAmountExpectedACY; "Cost Amount (Expected) (ACY)")
            {
            }
            column(CostAmountNonInvtblACY; "Cost Amount (Non-Invtbl.)(ACY)")
            {
            }
            column(ExpectedCostPostedtoGL; "Expected Cost Posted to G/L")
            {
            }
            column(ExpCostPostedtoGLACY; "Exp. Cost Posted to G/L (ACY)")
            {
            }
            column(DimensionSetID; "Dimension Set ID")
            {
            }
            column(ShortcutDimension3Code; "Shortcut Dimension 3 Code")
            {
            }
            column(ShortcutDimension4Code; "Shortcut Dimension 4 Code")
            {
            }
            column(ShortcutDimension5Code; "Shortcut Dimension 5 Code")
            {
            }
            column(ShortcutDimension6Code; "Shortcut Dimension 6 Code")
            {
            }
            column(ShortcutDimension7Code; "Shortcut Dimension 7 Code")
            {
            }
            column(ShortcutDimension8Code; "Shortcut Dimension 8 Code")
            {
            }
            column(JobNo; "Job No.")
            {
            }
            column(JobTaskNo; "Job Task No.")
            {
            }
            column(JobLedgerEntryNo; "Job Ledger Entry No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(Adjustment; Adjustment)
            {
            }
            column(AverageCostException; "Average Cost Exception")
            {
            }
            column(CapacityLedgerEntryNo; "Capacity Ledger Entry No.")
            {
            }
            column("Type"; "Type")
            {
            }
            column(No; "No.")
            {
            }
            column(ReturnReasonCode; "Return Reason Code")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(SystemCreatedBy; SystemCreatedBy)
            {
            }
            column(SystemId; SystemId)
            {
            }
            column(SystemModifiedAt; SystemModifiedAt)
            {
            }
            column(SystemModifiedBy; SystemModifiedBy)
            {
            }
            // dataitem(Item_Ledger_Entry; "Item Ledger Entry")
            // {
            //     DataItemLink = "Entry No." = ValueEntry."Item Ledger Entry No.";

            //     column(LotNo; "Lot No.")
            //     {
            //     }
            // }
        }
    }

    trigger OnBeforeOpen()
    begin

    end;
}