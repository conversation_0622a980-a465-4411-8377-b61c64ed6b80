table 60006 "Temperature Parameter SMK"
{
    Caption = 'Temperature Parameter';
    DataClassification = CustomerContent;
    DrillDownPageId = "Temperature Parameters SMK";
    LookupPageId = "Temperature Parameters SMK";

    fields
    {
        field(1; "Machine No."; Code[10])
        {
            Caption = 'Machine No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Machine No. field.';
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(3; "Temperature (°C)"; Decimal)
        {
            Caption = 'Temperature (°C)';
            ToolTip = 'Specifies the value of the Temperature field.';
        }
        field(4; "Barrel No."; Code[10])
        {
            Caption = 'Barrel No.';
            TableRelation = "Barrel SMK";
            ToolTip = 'Specifies the value of the Barrel No. field.';
        }

    }
    keys
    {
        key(PK; "Machine No.", "Item No.", "Barrel No.")
        {
            Clustered = true;
        }
    }
}