{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "SUMIKA_DEV3",
            "request": "launch",
            "type": "al",
            "environmentType": "Sandbox",
            "environmentName": "SUMIKA_DEV3",
            "tenant": "851f6719-46a2-43ac-8f03-4a84fc1fbffc",
            "startupObjectId": 60024,
            "startupObjectType": "Page",
            "breakOnError": "ExcludeTry",
            "launchBrowser": false,
            "enableLongRunningSqlStatements": true,
            "enableSqlInformationDebugger": true,
            //"schemaUpdateMode": "ForceSync"
        },
    
        // {
        //     "name": "SUMIKA_DEV_4",
        //     "request": "launch",
        //     "type": "al",
        //     "environmentType": "Sandbox",
        //     "environmentName": "SUMIKA_DEV4",
        //     "tenant": "851f6719-46a2-43ac-8f03-4a84fc1fbffc",
        //     "startupObjectId": 99000831,
        //     "startupObjectType": "Page",
        //     "breakOnError": "ExcludeTry",
        //     "launchBrowser": true,
        //     "enableLongRunningSqlStatements": true,
        //     "enableSqlInformationDebugger": true,
        //     //"schemaUpdateMode": "ForceSync"
        // }
    ]
}