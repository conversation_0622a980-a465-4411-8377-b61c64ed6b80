codeunit 60000 "Sumika Production Mgt. SMK"
{
    SingleInstance = true;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Calculate Prod. Order", OnAfterTransferBOMComponent, '', false, false)]
    local procedure OnAfterTransferBOMComponent(var ProdOrderLine: Record "Prod. Order Line"; var ProductionBOMLine: Record "Production BOM Line"; var ProdOrderComponent: Record "Prod. Order Component"; LineQtyPerUOM: Decimal; ItemQtyPerUOM: Decimal)
    begin
        ProdOrderComponent."Feeder No. SMK" := ProductionBOMLine."Feeder No. SMK";
    end;

    procedure PopulateProductionParameters(var ProductionOrder: Record "Production Order")
    var
        MachineCenter: Record "Machine Center";
        ProductionParameter: Record "Production Parameter SMK";
    begin
        if not ProductionParameter.Get(ProductionOrder."Machine No. SMK", ProductionOrder."Source No.") then begin
            ProductionOrder.Validate("Screw Speed (rpm) SMK", '');
            ProductionOrder.Validate("Torque (%) SMK", '');
            ProductionOrder.Validate("Vaccuum (bar) SMK", '');
            ProductionOrder.Validate("Output (kg/saat) SMK", '');
            ProductionOrder.Validate("Bath Temperature (°C) SMK", '');
            ProductionOrder.Validate("Filter Diameter (Mesh) SMK", '');
            ProductionOrder.Validate("Machine Scale No. SMK", '');
            ProductionOrder.Validate("Pellet. Speed (m/min) SMK", 0);
            ProductionOrder.Validate("Pellet Temperature (°C) SMK", 0);
        end
        else begin
            ProductionOrder.Validate("Screw Speed (rpm) SMK", ProductionParameter."Screw Speed (rpm)");
            ProductionOrder.Validate("Torque (%) SMK", ProductionParameter."Torque (%)");
            ProductionOrder.Validate("Vaccuum (bar) SMK", ProductionParameter."Vaccuum (bar)");
            ProductionOrder.Validate("Output (kg/saat) SMK", ProductionParameter."Output (kg/saat)");
            ProductionOrder.Validate("Bath Temperature (°C) SMK", ProductionParameter."Bath Temperature");
            ProductionOrder.Validate("Filter Diameter (Mesh) SMK", ProductionParameter."Filter Diameter (Mesh)");
            ProductionOrder.Validate("Pellet. Speed (m/min) SMK", ProductionParameter."Pelletization Speed (m/min)");
            ProductionOrder.Validate("Pellet Temperature (°C) SMK", ProductionParameter."Pellet Temperature (°C)");
            if MachineCenter.Get(ProductionOrder."Machine No. SMK") then
                ProductionOrder.Validate("Machine Scale No. SMK", MachineCenter."Scale No. SMK");
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Production Order", OnBeforeAssignItemNo, '', false, false)]
    local procedure OnBeforeAssignItemNo(var ProdOrder: Record "Production Order"; xProdOrder: Record "Production Order"; var Item: Record Item; CallingFieldNo: Integer)
    begin
        PopulateProductionParameters(ProdOrder);
        SetPackagingType(ProdOrder, Item);
    end;

    local procedure SetPackagingType(var ProdOrder: Record "Production Order"; var Item: Record Item)
    begin
        ProdOrder."Packaging Type SMK" := Item."Packaging Type SMK";
    end;

    procedure CopyProductionParameterRecord(ProductionParameter: Record "Production Parameter SMK"; NewMachineCenterNo: Code[10]; NewItemNo: Code[20])
    var
        NewProductionParameter: Record "Production Parameter SMK";
        NewTemperatureParameter: Record "Temperature Parameter SMK";
        TemperatureParameter: Record "Temperature Parameter SMK";
        CopiedMsg: Label 'Production Parameters copied.';

    begin
        NewProductionParameter.Init();
        NewProductionParameter.TransferFields(ProductionParameter);
        NewProductionParameter.Validate("Item No.", NewItemNo);
        NewProductionParameter."Machine No." := NewMachineCenterNo;
        NewProductionParameter.Insert(true);

        TemperatureParameter.SetRange("Machine No.", ProductionParameter."Machine No.");
        TemperatureParameter.SetRange("Item No.", ProductionParameter."Item No.");
        if TemperatureParameter.FindSet() then
            repeat
                NewTemperatureParameter.Init();
                NewTemperatureParameter.TransferFields(TemperatureParameter);
                NewTemperatureParameter."Machine No." := NewMachineCenterNo;
                NewTemperatureParameter."Item No." := NewItemNo;
                NewTemperatureParameter.Insert(true);
            until TemperatureParameter.Next() = 0;

        Message(CopiedMsg);
    end;

    procedure AssignLotNo(var ProdOrderLine: Record "Prod. Order Line")
    var
        Location: Record Location;
        Item: Record Item;
        NoSeriesMgt: Codeunit "No. Series";
    begin
        if ProdOrderLine."Lot No. SMK" <> '' then
            exit;

        ProdOrderLine.TestField("Location Code");

        Location.Get(ProdOrderLine."Location Code");
        if Location."Item Lot Nos. SMK" <> '' then
            ProdOrderLine.Validate("Lot No. SMK", NoSeriesMgt.GetNextNo(Location."Item Lot Nos. SMK", WorkDate(), true))
        else begin
            Item.Get(ProdOrderLine."Item No.");
            Item.TestField("Lot Nos.");
            ProdOrderLine.Validate("Lot No. SMK", NoSeriesMgt.GetNextNo(Item."Lot Nos.", WorkDate(), true));
        end;
        ProdOrderLine.Modify(true);

        CreateLotNoInformationFromProdOrderLine(ProdOrderLine);
    end;

    procedure CreateLotNoInformationFromProdOrderLine(ProdOrderLine: Record "Prod. Order Line")
    var
        LotNoInformation: Record "Lot No. Information";
    begin
        LotNoInformation.Init();
        LotNoInformation."Item No." := ProdOrderLine."Item No.";
        LotNoInformation."Variant Code" := ProdOrderLine."Variant Code";
        LotNoInformation."Lot No." := ProdOrderLine."Lot No. SMK";
        LotNoInformation.Insert(true);
        LotNoInformation.Validate(Description, ProdOrderLine.Description);
        LotNoInformation.Validate("Company No. SMK", 'SPCTR');
        LotNoInformation.Validate("Company Name SMK", 'SPCTR');

        LotNoInformation.Modify(true);

        SumikaQualityCtrlMgt.CreateQualityControlDocumentFromLotNoInformation(LotNoInformation, Enum::"Quality Control Type SMK"::Production);
    end;

    procedure PopulateAndOpenPackageCreation(ProdOrderLine: Record "Prod. Order Line"; PackageCreationType: Enum "Package Creation Type SMK")
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        TempPackageCreation: Record "Package Creation SMK" temporary;
        ProductionOrder: Record "Production Order";
        IncreaseQtyQst: Label 'Do you want to increase Quantity on Production Order in order to continue Package Creation process?';
        ProcessAbortedErr: Label 'Process aborted.';
    begin
        if ProdOrderLine."Lot No. SMK" = '' then
            AssignLotNo(ProdOrderLine);

        Item.Get(ProdOrderLine."Item No.");
        Item.TestField("Item Tracking Code");
        ItemTrackingCode.Get(Item."Item Tracking Code");
        ItemTrackingCode.TestField("Package Specific Tracking");

        ProductionOrder.Get(ProdOrderLine.Status, ProdOrderLine."Prod. Order No.");
        if ProdOrderLine."Finished Quantity" >= ProductionOrder.Quantity then
            if not ConfirmManagement.GetResponseOrDefault(IncreaseQtyQst, true) then
                Error(ProcessAbortedErr);

        TempPackageCreation.Init();
        TempPackageCreation.Insert(false);
#pragma warning disable LC0078
        TempPackageCreation.Validate(Type, PackageCreationType);
#pragma warning restore LC0078
        TempPackageCreation."Item No." := ProdOrderLine."Item No.";
        TempPackageCreation."Variant Code" := ProdOrderLine."Variant Code";
        TempPackageCreation."Item Description" := ProdOrderLine.Description;
        TempPackageCreation."Lot No." := ProdOrderLine."Lot No. SMK";
        TempPackageCreation."Source Document No." := ProdOrderLine."Prod. Order No.";
        TempPackageCreation."Source Document Line No." := ProdOrderLine."Line No.";
        TempPackageCreation."Entry Type" := TempPackageCreation."Entry Type"::Production;
        TempPackageCreation.Modify(false);

        Page.Run(Page::"Package Creation Worksheet SMK", TempPackageCreation);
    end;

    procedure CreatePackage(var TempPackageCreation: Record "Package Creation SMK" temporary; var ProdOrderLine: Record "Prod. Order Line"; PackageQty: Decimal)
    var
        ProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
        NoSeries: Codeunit "No. Series";
    begin
        ProdOrderLineDetail.Init();
        ProdOrderLineDetail.Status := ProdOrderLine.Status;
        ProdOrderLineDetail."Production Order No." := TempPackageCreation."Source Document No.";
        ProdOrderLineDetail."Production Order Line No." := TempPackageCreation."Source Document Line No.";
        ProdOrderLineDetail.Insert(true);
        ProdOrderLineDetail.Validate("Item No.", TempPackageCreation."Item No.");
        ProdOrderLineDetail.Validate("Variant Code", TempPackageCreation."Variant Code");
        ProdOrderLineDetail.Validate("Item Description", TempPackageCreation."Item Description");
        ProdOrderLineDetail.Validate("Location Code", ProdOrderLine."Location Code");
        ProdOrderLineDetail.Validate("Bin Code", ProdOrderLine."Bin Code");
        ProdOrderLineDetail.Validate(Quantity, PackageQty);
        if TempPackageCreation."Parent Package No." = '' then
            ProdOrderLineDetail.Validate("Package No.", SumikaPurchaseManagement.AssignPackageNo())
        else
            ProdOrderLineDetail.Validate("Package No.", NoSeries.GetNextNo(SumikaSetup."Parent Package No. Series", WorkDate(), false));
        ProdOrderLineDetail.Validate("Lot No.", ProdOrderLine."Lot No. SMK");
        ProdOrderLineDetail.Validate("Parent Package No.", TempPackageCreation."Parent Package No.");
        ProdOrderLineDetail.Modify(true);

        CreatePackageNoInformationFromProdOrderLineDetail(ProdOrderLineDetail);
    end;

    procedure CreatePackageNoInformationFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail SMK")
    var
        LotNoInformation: Record "Lot No. Information";
        PackageNoInformation: Record "Package No. Information";
    begin
        LotNoInformation.Get(ProdOrderLineDetail."Item No.", ProdOrderLineDetail."Variant Code", ProdOrderLineDetail."Lot No.");

        PackageNoInformation.Init();
        PackageNoInformation."Item No." := ProdOrderLineDetail."Item No.";
        PackageNoInformation."Variant Code" := ProdOrderLineDetail."Variant Code";
        PackageNoInformation."Package No." := ProdOrderLineDetail."Package No.";
        PackageNoInformation.Insert(true);
        PackageNoInformation.Validate(Description, ProdOrderLineDetail."Item Description");
        PackageNoInformation.Validate("Lot No. SMK", ProdOrderLineDetail."Lot No.");
        PackageNoInformation.Validate("Inventory Entry Date SMK", WorkDate());
        PackageNoInformation.Validate("Parent Package No. SMK", ProdOrderLineDetail."Parent Package No.");
        PackageNoInformation.Validate("Package Order No. SMK", CalculatePackageOrderNoByLotNo(PackageNoInformation));
        PackageNoInformation.Validate("Production Order No. SMK", ProdOrderLineDetail."Production Order No.");
        PackageNoInformation.Validate("Label Quantity SMK", ProdOrderLineDetail.Quantity);
        PackageNoInformation.Modify(true);
    end;

    [EventSubscriber(ObjectType::Report, Report::"Refresh Production Order", OnBeforeCalcProdOrder, '', false, false)]
    local procedure OnBeforeCalcProdOrder(var ProductionOrder: Record "Production Order"; Direction: Option)
    begin
        ProductionOrder.TestField("Location Code");
        ProductionOrder.TestField("Bin Code");
    end;

    local procedure CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail SMK")
    var
        ItemJournalLine: Record "Item Journal Line";
        ProductionOrder: Record "Production Order";
        CalcConsumption: Report "Calc. Consumption";
        CalcBasedOn: Option "Actual Output","Expected Output";

    begin
        ProdOrderLineDetail.TestField(Posted, false);

        SumikaSetup.Get();
        SumikaSetup.TestField("Consumption Jnl. Template Name");
        SumikaSetup.TestField("Consumption Jnl. Batch Name");

        ProductionOrder.Get(ProdOrderLineDetail.Status, ProdOrderLineDetail."Production Order No.");
        ProductionOrder.SetRecFilter();

        CalcConsumption.UseRequestPage(false);
        CalcConsumption.SetTableView(ProductionOrder);
        CalcConsumption.InitializeRequest(WorkDate(), CalcBasedOn::"Expected Output");
        CalcConsumption.SetTemplateAndBatchName(SumikaSetup."Consumption Jnl. Template Name", SumikaSetup."Consumption Jnl. Batch Name");
        GlobalOutputQty := ProdOrderLineDetail.Quantity;
        CalcConsumption.RunModal();
        GlobalOutputQty := 0;

        ItemJournalLine.SetRange("Journal Template Name", SumikaSetup."Consumption Jnl. Template Name");
        ItemJournalLine.SetRange("Journal Batch Name", SumikaSetup."Consumption Jnl. Batch Name");
        ItemJournalLine.FindFirst();
        Codeunit.Run(Codeunit::"Item Jnl.-Post Batch", ItemJournalLine);
    end;

    [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnBeforeGetNeededQty, '', false, false)]
    local procedure "Calc. Consumption_OnBeforeGetNeededQty"(var NeededQty: Decimal; CalcBasedOn: Option; ProdOrderComponent: Record "Prod. Order Component"; ProductionOrder: Record "Production Order"; PostingDate: Date; var IsHandled: Boolean)
    begin
        // Handle manual output quantity first (takes precedence)
        if UseManualOutput and (GlobalManualOutputQty > 0) then begin
            IsHandled := true;
            NeededQty := ProdOrderComponent."Quantity per" * GlobalManualOutputQty;
            exit;
        end;

        // Handle existing GlobalOutputQty logic for backward compatibility
        if GlobalOutputQty > 0 then begin
            IsHandled := true;
            NeededQty := ProdOrderComponent."Quantity per" * GlobalOutputQty;
        end;
    end;

    [EventSubscriber(ObjectType::Page, Page::"Consumption Journal", OnBeforeActionEvent, "Calc. Co&nsumption", false, false)]
    local procedure OnBeforeActionEvent_CalcConsumption_ConsumptionJournal(var Rec: Record "Item Journal Line")
    begin
        SkipAutomaticItemTrackingAssignment := true;
    end;

    [EventSubscriber(ObjectType::Page, Page::"Consumption Journal", OnAfterActionEvent, "Calc. Co&nsumption", false, false)]
    local procedure OnAfterActionEvent_CalcConsumption_ConsumptionJournal(var Rec: Record "Item Journal Line")
    begin
        SkipAutomaticItemTrackingAssignment := false;
    end;

    [EventSubscriber(ObjectType::Report, Report::"Calc. Consumption", OnAfterCreateConsumpJnlLine, '', false, false)]
    local procedure "Calc. Consumption_OnAfterCreateConsumpJnlLine"(LocationCode: Code[10]; BinCode: Code[20]; QtyToPost: Decimal; var ItemJournalLine: Record "Item Journal Line")
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
        RequiredQty: Decimal;
        QtyIsNotEnaughErr: Label 'Item No.: %1, Variant Code: %2 - %4 is not enough in %3', Comment = 'Item No.: %1, Variant Code: %2 is not enaugh in %3, %4 - Description';
    begin
        if SkipAutomaticItemTrackingAssignment then
            exit;

        RequiredQty := ItemJournalLine.Quantity;

        ItemLedgerEntry.SetCurrentKey("Package No.");
        ItemLedgerEntry.SetRange("Item No.", ItemJournalLine."Item No.");
        ItemLedgerEntry.SetRange("Variant Code", ItemJournalLine."Variant Code");
        ItemLedgerEntry.SetRange("Location Code", ItemJournalLine."Location Code");
        ItemLedgerEntry.SetRange(Open, true);
        if ItemLedgerEntry.FindSet(false) then
            repeat
                if ItemLedgerEntry."Remaining Quantity" < RequiredQty then begin
                    AssignLotNoToItemJournalLine(ItemJournalLine, ItemLedgerEntry."Lot No.", ItemLedgerEntry."Remaining Quantity", ItemLedgerEntry."Remaining Quantity", '', ItemLedgerEntry."Package No.");
                    RequiredQty -= ItemLedgerEntry."Remaining Quantity";
                end
                else begin
                    AssignLotNoToItemJournalLine(ItemJournalLine, ItemLedgerEntry."Lot No.", RequiredQty, RequiredQty, '', ItemLedgerEntry."Package No.");
                    RequiredQty := 0;
                    break;
                end;
            until (ItemLedgerEntry.Next() = 0);

        if RequiredQty <> 0 then
            Error(QtyIsNotEnaughErr, ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Location Code", ItemJournalLine.Description);

    end;

    procedure AssignLotNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line"; LotNo: Code[50]; QtyBase: Decimal; Qty: Decimal; NewLotNo: Code[50]; PackageNo: Code[50])
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        ItemJnlLineReserve: Codeunit "Item Jnl. Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        ItemJnlLineReserve.InitFromItemJnlLine(TempSourceTrackingSpecification, ItemJournalLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Lot No." := LotNo;
        TempTrackingSpecification."New Lot No." := NewLotNo;
        TempTrackingSpecification."Package No." := PackageNo;

        TempTrackingSpecification.SetQuantities(QtyBase,
                                                Qty,
                                                QtyBase,
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);
        ItemTrackingLines.SetBlockCommit(true);
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, ItemJournalLine."Posting Date", TempTrackingSpecification);

    end;

    procedure CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail: Record "Prod. Order Line Detail SMK")
    var
        ItemJournalLine: Record "Item Journal Line";
        LastItemJournalLine: Record "Item Journal Line";
        ProdOrderLine: Record "Prod. Order Line";
        OutputJnlExplRoute: Codeunit "Output Jnl.-Expl. Route";
        ItemJournalLineNo: Integer;
    begin
        ProdOrderLine.Get(ProdOrderLineDetail.Status, ProdOrderLineDetail."Production Order No.", ProdOrderLineDetail."Production Order Line No.");
        if ProdOrderLine.Quantity <= ProdOrderLine."Finished Quantity" then begin
            ProdOrderLine.Validate(Quantity, ProdOrderLine."Finished Quantity" + ProdOrderLineDetail.Quantity);
            ProdOrderLine.Modify(true);
        end;

        ProdOrderLineDetail.CalcFields("Automatic Consumption");
        if ProdOrderLineDetail."Automatic Consumption" then
            CreateConsumptionJournalsFromProdOrderLineDetail(ProdOrderLineDetail);

        ProdOrderLineDetail.TestField(Posted, false);

        SumikaSetup.GetRecordOnce();
        SumikaSetup.TestField("Output Journal Template Name");
        SumikaSetup.TestField("Output Journal Batch Name");

        ItemJournalLineNo := 10000;
        LastItemJournalLine.SetRange("Journal Template Name", SumikaSetup."Output Journal Template Name");
        LastItemJournalLine.SetRange("Journal Batch Name", SumikaSetup."Output Journal Batch Name");
        if LastItemJournalLine.FindLast() then
            ItemJournalLineNo := LastItemJournalLine."Line No." + 10000;

        ItemJournalLine.Init();
        ItemJournalLine.Validate("Journal Template Name", SumikaSetup."Output Journal Template Name");
        ItemJournalLine.Validate("Journal Batch Name", SumikaSetup."Output Journal Batch Name");
        ItemJournalLine.Validate("Line No.", ItemJournalLineNo);
        ItemJournalLine.SetUpNewLine(LastItemJournalLine);
        ItemJournalLine.Validate("Entry Type", ItemJournalLine."Entry Type"::Output);
        ItemJournalLine.Insert(true);

        ItemJournalLine.Validate("Order No.", ProdOrderLineDetail."Production Order No.");
        ItemJournalLine.Validate("Item No.", ProdOrderLineDetail."Item No.");
        ItemJournalLine.Validate("Lot No.", ProdOrderLineDetail."Lot No.");
        ItemJournalLine.Validate("Package No.", ProdOrderLineDetail."Package No.");
        ItemJournalLine.Modify(true);

        GlobalOutputQty := ProdOrderLineDetail.Quantity;
        GlobalProdOrderLineDetail := ProdOrderLineDetail;

        OutputJnlExplRoute.Run(ItemJournalLine);

        Codeunit.Run(Codeunit::"Item Jnl.-Post Batch", ItemJournalLine);

        ProdOrderLineDetail.Validate(Posted, true);
        ProdOrderLineDetail.Modify(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnBeforeOutputItemJnlLineInsert, '', false, false)]
    local procedure OnBeforeOutputItemJnlLineInsert(var ItemJournalLine: Record "Item Journal Line"; LastOperation: Boolean)
    begin
        ItemJournalLine.Validate("Output Quantity", GlobalOutputQty);
        ItemJournalLine.Validate("Location Code", GlobalProdOrderLineDetail."Location Code");
        ItemJournalLine.Validate("Bin Code", GlobalProdOrderLineDetail."Bin Code");

        if not ItemJournalLine.LastOutputOperation(ItemJournalLine) then begin
            ItemJournalLine.Validate("Lot No.", '');
            ItemJournalLine.Validate("Package No.", '');
        end;
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Output Jnl.-Expl. Route", OnAfterInsertItemJnlLine, '', false, false)]
    local procedure OnAfterInsertItemJnlLine(var ItemJournalLine: Record "Item Journal Line")
    begin
        if ItemJournalLine.LastOutputOperation(ItemJournalLine) then
            AssignLotAndPackageNoToItemJournalLine(ItemJournalLine)
    end;

    local procedure AssignLotAndPackageNoToItemJournalLine(var ItemJournalLine: Record "Item Journal Line")
    var
        TempReservEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservStatus: Enum "Reservation Status";
    begin
        TempReservEntry.Init();
        TempReservEntry."Entry No." := 1;
        TempReservEntry."Lot No." := ItemJournalLine."Lot No.";
        TempReservEntry."Package No." := ItemJournalLine."Package No.";
        TempReservEntry.Quantity := ItemJournalLine."Output Quantity";
        TempReservEntry.Insert(false);

        ItemJournalLine."Lot No." := '';
        ItemJournalLine."Package No." := '';
        ItemJournalLine.Modify(true);

        CreateReservEntry.CreateReservEntryFor(
          Database::"Item Journal Line", ItemJournalLine."Entry Type".AsInteger(),
          ItemJournalLine."Journal Template Name", ItemJournalLine."Journal Batch Name", 0, ItemJournalLine."Line No.", ItemJournalLine."Qty. per Unit of Measure",
          TempReservEntry.Quantity, TempReservEntry.Quantity * ItemJournalLine."Qty. per Unit of Measure", TempReservEntry);

        CreateReservEntry.CreateEntry(
          ItemJournalLine."Item No.", ItemJournalLine."Variant Code", ItemJournalLine."Location Code", '', ItemJournalLine."Posting Date", 0D, 0, ReservStatus::Prospect);
    end;

    local procedure SetProductionOrderNoSeriesForUserID(var NoSeriesCode: Code[20]; var IsHandled: Boolean)
    var
        UserSetup: Record "User Setup";
    begin
        if not UserSetup.Get(UserId()) then
            exit;

        if UserSetup."Prod. Order No. Series SMK" = '' then
            exit;

        IsHandled := true;
        NoSeriesCode := UserSetup."Prod. Order No. Series SMK";
    end;

    [EventSubscriber(ObjectType::Table, Database::"Item Journal Line", OnBeforeValidateLocationCode, '', false, false)]
    local procedure OnBeforeValidateLocationCode(var ItemJournalLine: Record "Item Journal Line"; xItemJournalLine: Record "Item Journal Line"; var IsHandled: Boolean)
    var
        Location: Record Location;
    begin
        if ItemJournalLine."Entry Type" <> ItemJournalLine."Entry Type"::Consumption then
            exit;

        if not Location.Get(ItemJournalLine."Location Code") then
            exit;
    end;

    procedure AssignParentPackageNo(var PackageCreation: Record "Package Creation SMK"; CombinedReceivingLine: Record "Combined Receiving Line SMK")
    var
        CombinedReceivingLineDetail: Record "CombinedReceivingLineDtl SMK";
        PackageNoInformation: Record "Package No. Information";
    begin
        if PackageCreation."Parent Package No." <> '' then
            exit;

        SumikaSetup.GetRecordOnce();
        SumikaSetup.TestField("Parent Package No. Series");

        PackageNoInformation.Init();
        PackageNoInformation."Item No." := PackageCreation."Item No.";
        PackageNoInformation."Variant Code" := PackageCreation."Variant Code";
        PackageNoInformation."Package No." := SumikaPurchaseManagement.AssignPackageNo();
        PackageNoInformation.Insert(true);
        PackageNoInformation.Validate(Description, PackageCreation."Item Description");
        PackageNoInformation.Validate("Lot No. SMK", PackageCreation."Lot No.");
        PackageNoInformation.Validate("Inventory Entry Date SMK", WorkDate());
        PackageNoInformation.Validate("Label Quantity SMK", PackageCreation."Qty. to Receive");
        PackageNoInformation.Modify(true);

        PackageCreation.Validate("Parent Package No.", PackageNoInformation."Package No.");
        PackageCreation.Modify(true);

        CombinedReceivingLineDetail.Init();
        CombinedReceivingLineDetail."Document No." := PackageCreation."Source Document No.";
        CombinedReceivingLineDetail."Document Line No." := PackageCreation."Source Document Line No.";
        CombinedReceivingLineDetail.Insert(true);
        CombinedReceivingLineDetail.Validate("Purchase Order No.", CombinedReceivingLine."Source Document No.");
        CombinedReceivingLineDetail.Validate("Purchase Order Line No.", CombinedReceivingLine."Source Document Line No.");
        CombinedReceivingLineDetail.Validate("Item No.", PackageCreation."Item No.");
        CombinedReceivingLineDetail.Validate("Variant Code", PackageCreation."Variant Code");
        CombinedReceivingLineDetail.Validate("Item Description", PackageCreation."Item Description");
        CombinedReceivingLineDetail.Validate("Location Code", CombinedReceivingLine."Location Code");
        CombinedReceivingLineDetail.Validate("Bin Code", CombinedReceivingLine."Bin Code");
        CombinedReceivingLineDetail.Validate("Qty. to Receive", PackageCreation."Qty. to Receive");
        CombinedReceivingLineDetail.Validate("Package No.", PackageNoInformation."Package No.");
        CombinedReceivingLineDetail.Validate("Lot No.", CombinedReceivingLine."Lot No.");
        CombinedReceivingLineDetail.Modify(true);
    end;

    procedure AssignParentPackageNo(var PackageCreation: Record "Package Creation SMK"; ProdOrderLine: Record "Prod. Order Line")
    var
        PackageNoInformation: Record "Package No. Information";
        ProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
    begin
        if PackageCreation."Parent Package No." <> '' then
            exit;

        SumikaSetup.GetRecordOnce();
        SumikaSetup.TestField("Parent Package No. Series");

        PackageNoInformation.Init();
        PackageNoInformation."Item No." := PackageCreation."Item No.";
        PackageNoInformation."Variant Code" := PackageCreation."Variant Code";
        PackageNoInformation."Package No." := SumikaPurchaseManagement.AssignPackageNo();
        PackageNoInformation.Insert(true);
        PackageNoInformation.Validate(Description, PackageCreation."Item Description");
        PackageNoInformation.Validate("Lot No. SMK", PackageCreation."Lot No.");
        PackageNoInformation.Validate("Inventory Entry Date SMK", WorkDate());
        PackageNoInformation.Validate("Label Quantity SMK", PackageCreation."Qty. to Receive");
        PackageNoInformation.Validate("Production Order No. SMK", ProdOrderLine."Prod. Order No.");
        PackageNoInformation.Validate("Package Order No. SMK", CalculatePackageOrderNoByLotNo(PackageNoInformation));
        PackageNoInformation.Modify(true);

        PackageCreation.Validate("Parent Package No.", PackageNoInformation."Package No.");
        PackageCreation.Modify(true);

        ProdOrderLineDetail.Init();
        ProdOrderLineDetail.Status := ProdOrderLine.Status;
        ProdOrderLineDetail."Production Order No." := PackageCreation."Source Document No.";
        ProdOrderLineDetail."Production Order Line No." := PackageCreation."Source Document Line No.";
        ProdOrderLineDetail.Insert(true);
        ProdOrderLineDetail.Validate("Item No.", PackageCreation."Item No.");
        ProdOrderLineDetail.Validate("Variant Code", PackageCreation."Variant Code");
        ProdOrderLineDetail.Validate("Item Description", PackageCreation."Item Description");
        ProdOrderLineDetail.Validate("Location Code", ProdOrderLine."Location Code");
        ProdOrderLineDetail.Validate("Bin Code", ProdOrderLine."Bin Code");
        ProdOrderLineDetail.Validate(Quantity, PackageCreation."Qty. to Receive");
        ProdOrderLineDetail.Validate("Package No.", PackageNoInformation."Package No.");
        ProdOrderLineDetail.Validate("Lot No.", ProdOrderLine."Lot No. SMK");
        ProdOrderLineDetail.Modify(true);
    end;

    [EventSubscriber(ObjectType::Table, Database::"Production Order", OnBeforeGetNoSeriesCode, '', false, false)]
    local procedure OnBeforeGetNoSeriesCode(var ProductionOrder: Record "Production Order"; MfgSetup: Record "Manufacturing Setup"; var NoSeriesCode: Code[20]; var IsHandled: Boolean)
    begin
        SetProductionOrderNoSeriesForUserID(NoSeriesCode, IsHandled);
    end;

    procedure GetMachineScaleNoFromPackageNoInformation(PackageNoInformation: Record "Package No. Information"): Code[10]
    var
        ProductionOrder: Record "Production Order";
    begin
        ProductionOrder.SetRange("No.", PackageNoInformation."Production Order No. SMK");
        if not ProductionOrder.FindFirst() then
            exit('');

        exit(ProductionOrder."Machine Scale No. SMK");
    end;

    local procedure IsChildPackage(var PackageNoInformation: Record "Package No. Information"): Boolean
    begin
        if PackageNoInformation."Parent Package No. SMK" <> '' then
            exit(true);
    end;

    procedure CalculatePackageOrderNoByLotNo(var PackageNoInformation: Record "Package No. Information"): Integer
    var
        PackageNoInformation2: Record "Package No. Information";
    begin
        if IsChildPackage(PackageNoInformation) then begin
            PackageNoInformation2.Get(PackageNoInformation."Item No.", PackageNoInformation."Variant Code", PackageNoInformation."Parent Package No. SMK");
            exit(PackageNoInformation2."Package Order No. SMK");
        end;

        PackageNoInformation2.SetRange("Lot No. SMK", PackageNoInformation."Lot No. SMK");
        PackageNoInformation2.SetFilter("Production Order No. SMK", '<>''''');
        if not PackageNoInformation2.FindLast() then
            exit(1)
        else
            exit(PackageNoInformation2."Package Order No. SMK" + 1);
    end;

    procedure GetDescription2FromItemNo(ItemNo: Code[20]): Text[50]
    var
        Item: Record Item;
    begin
        if not Item.Get(ItemNo) then
            exit('');

        exit(Item."Description 2");
    end;

    procedure GetMachineNameFromProdOrderLine(ProdOrderLine: Record "Prod. Order Line"): Text[100]
    var
        MachineCenter: Record "Machine Center";
        ProductionOrder: Record "Production Order";
    begin
        if not ProductionOrder.Get(ProdOrderLine.Status, ProdOrderLine."Prod. Order No.") then
            exit('');

        if not MachineCenter.Get(ProductionOrder."Machine No. SMK") then
            exit('');

        exit(MachineCenter.Name);
    end;

    [EventSubscriber(ObjectType::Page, Page::"Package No. Information List", OnOpenPageEvent, '', false, false)]
    local procedure OnOpenPageEvent_PackageNoInformationList(var Rec: Record "Package No. Information")
    begin
        if RemoveInventoryExistFilter then
            Rec.SetRange("Inventory Exist SMK");
    end;

    procedure OnAfterDrillDownChieldPackageCount_ProdOrderLineDetails(var ProdOrderLineDetail: Record "Prod. Order Line Detail SMK")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        RemoveInventoryExistFilter := true;

        PackageNoInformation.SetRange("Parent Package No. SMK", ProdOrderLineDetail."Package No.");
        Page.RunModal(Page::"Package No. Information List", PackageNoInformation);

        RemoveInventoryExistFilter := false;
    end;

    procedure OnAfterLookupPackageNo_EnterResultPage(var QualityControlLineDtl: Record "Quality Control Line Dtl. SMK")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        RemoveInventoryExistFilter := true;

        PackageNoInformation.SetRange("Item No.", QualityControlLineDtl."Item No.");
        PackageNoInformation.SetRange("Variant Code", QualityControlLineDtl."Variant Code");
        PackageNoInformation.SetRange("Lot No. SMK", QualityControlLineDtl."Lot No.");
        if Page.RunModal(Page::"Package No. Information List", PackageNoInformation) = Action::LookupOK then
            QualityControlLineDtl.Validate("Package No.", PackageNoInformation."Package No.");

        RemoveInventoryExistFilter := false;
    end;

    procedure OnAfterLookupPackageNo_ReadBarcodePage(): Code[50]
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        RemoveInventoryExistFilter := true;

        if Page.RunModal(Page::"Package No. Information List", PackageNoInformation) = Action::LookupOK then
            exit(PackageNoInformation."Package No.");

        RemoveInventoryExistFilter := false;
    end;

    procedure CalculateTotalKGFromItemJournal(ItemJournalLine: Record "Item Journal Line"): Decimal
    var
        ItemJournalLine2: Record "Item Journal Line";
    begin
        ItemJournalLine2.SetRange("Journal Template Name", ItemJournalLine."Journal Template Name");
        ItemJournalLine2.SetRange("Journal Batch Name", ItemJournalLine."Journal Batch Name");
        ItemJournalLine2.SetFilter("Unit of Measure Code", 'KG');
        ItemJournalLine2.CalcSums("Quantity");

        exit(ItemJournalLine2.Quantity);
    end;

    procedure SetManualOutputParameters(UseManual: Boolean; ManualQty: Decimal)
    begin
        UseManualOutput := UseManual;
        GlobalManualOutputQty := ManualQty;
    end;

    var
        SumikaSetup: Record "Sumika Setup SMK";
        GlobalProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
        ConfirmManagement: Codeunit "Confirm Management";
        RemoveInventoryExistFilter: Boolean;
        GlobalOutputQty: Decimal;
        GlobalManualOutputQty: Decimal;
        UseManualOutput: Boolean;
        SkipAutomaticItemTrackingAssignment: Boolean;
}