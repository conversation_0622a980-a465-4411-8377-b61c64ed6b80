page 60011 "Item Quality Control Spec. SMK"
{
    ApplicationArea = All;
    Caption = 'Item - Quality Control Specifications';
    PageType = List;
    SourceTable = "Item Quality Control Spec. SMK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field(Type; Rec."Type")
                {
                }
                field("Specification Code"; Rec."Specification Code")
                {
                }
                field("Specification Description"; Rec."Specification Description")
                {
                }
                field("Specification Reference"; Rec."Specification Reference")
                {
                }
                field("Min Value"; Rec."Min Value")
                {
                }
                field("Max Value"; Rec."Max Value")
                {
                }
                field("Exact Value"; Rec."Exact Value")
                {
                }
                field("Text Value"; Rec."Selection Value")
                {
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                }
                field(Standard; Rec.Standard)
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(CopyItemQualityControlSpecs)
            {
                ApplicationArea = All;
                Caption = 'Copy Item - Quality Control Specs';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = CopyCostBudget;
                ToolTip = 'Executes the Copy Item - Quality Control Specs action.';
                PromotedOnly = true;
                trigger OnAction()
                var
                    ItemQualityControlSpec: Record "Item Quality Control Spec. SMK";
                begin
                    CurrPage.SetSelectionFilter(ItemQualityControlSpec);
                    DataSyncManagement.CopyItemQualityControlSpecs(ItemQualityControlSpec);
                end;
            }
        }
    }
    var
        DataSyncManagement: Codeunit "Data Sync. Management SMK";
}