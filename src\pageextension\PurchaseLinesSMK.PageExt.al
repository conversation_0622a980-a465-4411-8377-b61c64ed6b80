pageextension 60047 "Purchase Lines SMK" extends "Purchase Lines"
{
    layout
    {
        addlast(Control1)
        {
            field("CreatedByUserName SMK"; SumikaBasicFunctions.GetUserNameFromSecurityId(Rec.SystemCreatedBy))
            {
                ApplicationArea = All;
                Caption = 'Created By User Name';
                Editable = false;
                ToolTip = 'Specifies the name of the user who created the record.';
            }
        }
    }

    actions
    {
        addlast(Processing)
        {
            action(HeaderCommentsSMK)
            {
                ApplicationArea = All;
                Caption = 'Header Comments';
                Image = ViewComments;
                ToolTip = 'View or add comments for the header.';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                var
                    PurchCommentLine: Record "Purch. Comment Line";
                    PurchCommentSheet: Page "Purch. Comment Sheet";
                begin
                    PurchCommentLine.SetRange("Document Type", Rec."Document Type");
                    PurchCommentLine.SetRange("No.", Rec."Document No.");
                    PurchCommentLine.SetRange("Document Line No.", 0);  // 0 means header comments

                    PurchCommentSheet.SetTableView(PurchCommentLine);
                    PurchCommentSheet.RunModal();
                end;
            }

            action(LineCommentsSMK)
            {
                ApplicationArea = All;
                Caption = 'Line Comments';
                Image = ViewComments;
                ToolTip = 'View or add comments for the selected line.';
                Promoted = true;
                PromotedCategory = Process;

                trigger OnAction()
                var
                    PurchCommentLine: Record "Purch. Comment Line";
                    PurchCommentSheet: Page "Purch. Comment Sheet";
                begin
                    PurchCommentLine.SetRange("Document Type", Rec."Document Type");
                    PurchCommentLine.SetRange("No.", Rec."Document No.");
                    PurchCommentLine.SetRange("Document Line No.", Rec."Line No.");

                    PurchCommentSheet.SetTableView(PurchCommentLine);
                    PurchCommentSheet.RunModal();
                end;
            }
        }
    }

    var
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
}