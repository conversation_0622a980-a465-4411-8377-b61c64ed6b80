codeunit 60008 "Copy Item SMK"
{
    TableNo = Item;
    trigger OnRun()
    var
        ItemUnitofMeasure: Record "Item Unit of Measure";
    begin
        if not NewItem.Get(Rec."No.") then begin


            NewItem.Init();
            NewItem.TransferFields(Rec);

            NewItem."Routing No." := '';

            FieldChecks(Rec);
            NewItem.Insert(true);

            ItemUnitofMeasure.Init();
            ItemUnitofMeasure."Item No." := NewItem."No.";
            ItemUnitofMeasure.Code := NewItem."Base Unit of Measure";
            ItemUnitofMeasure.Insert(true);
        end
        else begin
            NewItem.TransferFields(Rec);

            FieldChecks(Rec);

            NewItem.Modify(true);
        end;
    end;

    local procedure FieldChecks(var Rec: Record Item)
    begin
        if not PackagingType.Get(Rec."Packaging Type SMK") then
            NewItem."Packaging Type SMK" := '';

        if not ItemCategory.Get(Rec."Item Category Code") then
            NewItem."Item Category Code" := '';

        if not GenProductPostingGroup.Get(Rec."Gen. Prod. Posting Group") then
            NewItem."Gen. Prod. Posting Group" := '';

        if not VATProductPostingGroup.Get(Rec."VAT Prod. Posting Group") then
            NewItem."VAT Prod. Posting Group" := '';

        if not InventoryPostingGroup.Get(Rec."Inventory Posting Group") then
            NewItem."Inventory Posting Group" := '';

        if not ProductionBOMHeader.Get(Rec."Production BOM No.") then
            NewItem."Production BOM No." := '';
    end;

    var
        NewItem: Record Item;
        PackagingType: Record "Packaging Type SMK";
        ItemCategory: Record "Item Category";
        GenProductPostingGroup: Record "Gen. Product Posting Group";
        VATProductPostingGroup: Record "VAT Product Posting Group";
        InventoryPostingGroup: Record "Inventory Posting Group";
        ProductionBOMHeader: Record "Production BOM Header";
}