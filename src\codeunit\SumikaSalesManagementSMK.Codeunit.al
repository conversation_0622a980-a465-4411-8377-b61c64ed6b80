codeunit 60004 "Sumika Sales Management SMK"
{
    procedure GetSalesOrderLines(CombinedShipmentHeader: Record "Combined Shipment Header SMK")
    var
        CombinedShipmentLine: Record "Combined Shipment Line SMK";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesReceivablesSetup: Record "Sales & Receivables Setup";
    begin
        CombinedShipmentHeader.TestField("Transferred to Sales Order", false);
        CombinedShipmentHeader.TestField("Customer No.");

        SalesReceivablesSetup.GetRecordOnce();
        SalesReceivablesSetup.TestField("Default Quantity to Ship", SalesReceivablesSetup."Default Quantity to Ship"::Blank);

        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.DeleteAll(true);

        SalesHeader.SetRange("Document Type", SalesHeader."Document Type"::Order);
        SalesHeader.SetRange("Sell-to Customer No.", CombinedShipmentHeader."Customer No.");
#pragma warning disable AA0210
        SalesHeader.SetRange("Completely Shipped", false);
#pragma warning restore AA0210
        SalesHeader.SetRange(Status, SalesHeader.Status::Released);
        if CombinedShipmentHeader."Sales Order No." <> '' then
            SalesHeader.SetRange("No.", CombinedShipmentHeader."Sales Order No.");

        SalesHeader.FindSet();
        repeat
            SalesLine.SetRange("Document Type", SalesHeader."Document Type");
            SalesLine.SetRange("Document No.", SalesHeader."No.");
            SalesLine.SetRange(Type, SalesLine.Type::Item);
            SalesLine.SetRange("Completely Shipped", false);
            if SalesLine.FindSet() then
                repeat
                    CombinedShipmentLine.Init();
                    CombinedShipmentLine."Document No." := CombinedShipmentHeader."No.";
                    CombinedShipmentLine.Insert(true);
                    CombinedShipmentLine.Validate("Source Document No.", SalesLine."Document No.");
                    CombinedShipmentLine.Validate("Source Document Line No.", SalesLine."Line No.");
                    CombinedShipmentLine.Validate("Item No.", SalesLine."No.");
                    CombinedShipmentLine.Validate("Variant Code", SalesLine."Variant Code");
                    CombinedShipmentLine.Validate("Item Description", SalesLine.Description);
                    CombinedShipmentLine.Validate("Unit of Measure Code", SalesLine."Unit of Measure Code");
                    CombinedShipmentLine.Validate(Quantity, SalesLine.Quantity);
                    CombinedShipmentLine.Validate("Qty. to Ship", SalesLine.Quantity); // Initialize Qty. to Ship
                    CombinedShipmentLine.Validate("Quantity Shipped", SalesLine."Quantity Shipped");
                    CombinedShipmentLine.Validate("Location Code", SalesLine."Location Code");
                    CombinedShipmentLine.Validate("Bin Code", SalesLine."Bin Code");
                    CombinedShipmentLine.Modify(true);
                until SalesLine.Next() = 0;
        until SalesHeader.Next() = 0;
    end;

    procedure ProcessLabel(var CombinedShipmentHeader: Record "Combined Shipment Header SMK")
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if CombinedShipmentHeader.Barcode = '' then
            exit;

        CombinedShipmentHeader.TestField("Transferred to Sales Order", false);

        // CombinedShipmentHeader.TestField("Shipment Location Code");
        // CombinedShipmentHeader.TestField("Shipment Bin Code");

        //PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.SetRange("Package No.", CombinedShipmentHeader.Barcode);
        PackageNoInformation.FindFirst();
        if PackageNoInformation.Inventory = 0 then begin
            PackageNoInformation.Reset();
            PackageNoInformation.SetRange("Parent Package No. SMK", CombinedShipmentHeader.Barcode);
            if PackageNoInformation.FindSet(false) then
                repeat
                    InsertCombinedShipmentLineDetail(CombinedShipmentHeader, PackageNoInformation);
                until PackageNoInformation.Next() = 0
        end
        else begin
            PackageNoInformation.Reset();
            PackageNoInformation.SetRange("Package No.", CombinedShipmentHeader.Barcode);
            PackageNoInformation.FindFirst();
            InsertCombinedShipmentLineDetail(CombinedShipmentHeader, PackageNoInformation);
        end;

        CombinedShipmentHeader.Barcode := '';
    end;

    local procedure InsertCombinedShipmentLineDetail(CombinedShipmentHeader: Record "Combined Shipment Header SMK"; PackageNoInformation: Record "Package No. Information")
    var
        LotNoInformation: Record "Lot No. Information";
        QualityControlHeader: Record "Quality Control Header SMK";
        Bin: Record Bin;
        CombinedShipmentLine: Record "Combined Shipment Line SMK";
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl SMK";
        CombinedShipmentLineDtl2: Record "CombinedShipmentLineDtl SMK";
        PackagingType: Record "Packaging Type SMK";
        Item: Record Item;
        ConfirmManagement: Codeunit "Confirm Management";
        CombinedShipmentLineFound: Boolean;
        PackageNo: Code[50];
        RequiredQty: Decimal;
        EmptyPackageErr: Label 'You can not process an empty package. Package No.: %1', Comment = '%1 is Package No.';
        MultipleSamePackageErr: Label 'Package No. %1 has been read more than once.', Comment = '%1 is Package No.';
        NoValidLineFoundErr: Label 'No valid Combined Shipment Line found for Package No.: %1 - Item No.: %2', Comment = '%1="Package No. Information"."Package No."; %2="Package No. Information"."Item No."';
        MoreQtyErr: Label 'You can not read more than %1 for Sales Order No.: %2 Sales Order Line No.: %3. You read %4 with Package No.: %5', Comment = '%1=; %2="Combined Shipment Line SMK"."Source Document No."; %3="Combined Shipment Line SMK"."Source Document Line No."; %4=; %5="Package No. Information"."Package No."';
        SplitPackageQst: Label 'The required quantity %1 is less than the package inventory %2. Do you want to split package %3?', Comment = '%1=Required Quantity; %2=Package Inventory; %3=Package No.';
        QualityControlErr: Label 'Quality Control is not accepted for Lot No.: %1 or Package No.: %2', Comment = '%1="Lot No. Information"."Lot No." %2="Package No. Information"."Package No."';
    begin
        PackageNoInformation.CalcFields(Inventory);

        if PackageNoInformation.Inventory = 0 then begin
            Message(EmptyPackageErr, PackageNoInformation."Package No.");
            exit;
        end;

        if LotNoInformation.Get(PackageNoInformation."Item No.", PackageNoInformation."Variant Code", PackageNoInformation."Lot No. SMK") then
            if QualityControlHeader.Get(LotNoInformation."Certificate Number") then
                if (QualityControlHeader.Status <> QualityControlHeader.Status::Acceptance) or
                   (PackageNoInformation."Quality Control Status SMK" <> PackageNoInformation."Quality Control Status SMK"::Acceptance) then
                    Error(QualityControlErr, LotNoInformation."Lot No.", PackageNoInformation."Package No.");

        CombinedShipmentLine.SetAutoCalcFields("Line Package Quantity");
        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.SetRange("Item No.", PackageNoInformation."Item No.");
        CombinedShipmentLine.SetRange("Variant Code", PackageNoInformation."Variant Code");
        if not CombinedShipmentLine.FindSet(false) then begin
            Message(NoValidLineFoundErr, PackageNoInformation."Package No.", PackageNoInformation."Item No.");
            exit;
        end;

        CombinedShipmentLineFound := false;
        repeat
            if (CombinedShipmentLine."Qty. to Ship" - CombinedShipmentLine."Quantity Shipped" - CombinedShipmentLine."Line Package Quantity") >= PackageNoInformation.Inventory then
                CombinedShipmentLineFound := true;
        until (CombinedShipmentLine.Next() = 0) or CombinedShipmentLineFound;

        PackageNo := PackageNoInformation."Package No.";

        if not CombinedShipmentLineFound then begin
            RequiredQty := CombinedShipmentLine."Qty. to Ship" - CombinedShipmentLine."Quantity Shipped" - CombinedShipmentLine."Line Package Quantity";
            Item.Get(PackageNoInformation."Item No.");

            PackagingType.Reset();
            PackagingType.SetRange(Code, Item."Packaging Type SMK");
            if PackagingType.FindFirst() and PackagingType."Allow Package Split" then
                // Ask for confirmation to split the package
                if ConfirmManagement.GetResponseOrDefault(StrSubstNo(SplitPackageQst, RequiredQty, PackageNoInformation.Inventory, PackageNoInformation."Package No."), false) then begin
                    // User confirmed to split the package
                    PackageNo := CreateAndPostPackageSplitDocumentForShipment(PackageNoInformation, CombinedShipmentLine, RequiredQty);
                    PackageNoInformation.Get(CombinedShipmentLine."Item No.", CombinedShipmentLine."Variant Code", PackageNo);
                    PackageNoInformation.CalcFields(Inventory);
                    CombinedShipmentLineFound := true;
                end;

            // If no split is allowed or user declined, show the original error message
            if not CombinedShipmentLineFound then begin
                Message(MoreQtyErr, RequiredQty,
                        CombinedShipmentLine."Source Document No.",
                        CombinedShipmentLine."Source Document Line No.",
                        PackageNoInformation.Inventory,
                        PackageNoInformation."Package No.");
                exit;
            end;
        end;


        Bin.SetRange("Location Code", CombinedShipmentLine."Location Code");
        Bin.SetRange("Shipment Bin SMK", true);
        if not Bin.FindFirst() then
            Error(ShipmentBinNotDefinedErr, CombinedShipmentLine."Location Code");

        CombinedShipmentLineDtl2.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLineDtl2.SetRange("Package No.", PackageNoInformation."Package No.");

        //if CombinedShipmentLineDtl2.Find('-') then //and (CombinedShipmentLineDtl2.Next() > 0) then
        if not CombinedShipmentLineDtl2.IsEmpty() then
            Error(MultipleSamePackageErr, PackageNoInformation."Package No.");

        CombinedShipmentLineDtl.Init();
        CombinedShipmentLineDtl."Document No." := CombinedShipmentLine."Document No.";
        CombinedShipmentLineDtl."Document Line No." := CombinedShipmentLine."Line No.";
        CombinedShipmentLineDtl.Insert(true);
        CombinedShipmentLineDtl.Validate("Source Document No.", CombinedShipmentLine."Source Document No.");
        CombinedShipmentLineDtl.Validate("Source Document Line No.", CombinedShipmentLine."Source Document Line No.");
        CombinedShipmentLineDtl.Validate("Item No.", CombinedShipmentLine."Item No.");
        CombinedShipmentLineDtl.Validate("Variant Code", CombinedShipmentLine."Variant Code");
        CombinedShipmentLineDtl.Validate("Item Description", CombinedShipmentLine."Item Description");
        CombinedShipmentLineDtl.Validate("Location Code", CombinedShipmentLine."Location Code");
        CombinedShipmentLineDtl.Validate("Bin Code", CombinedShipmentLine."Bin Code");
        CombinedShipmentLineDtl.Validate(Quantity, PackageNoInformation.Inventory);
        CombinedShipmentLineDtl.Validate("Package No.", PackageNoInformation."Package No.");
        CombinedShipmentLineDtl.Validate("Parent Package No.", PackageNoInformation."Parent Package No. SMK");
        CombinedShipmentLineDtl.Validate("Lot No.", PackageNoInformation."Lot No. SMK");
        if (PackageNoInformation."Location Code SMK" = CombinedShipmentLine."Location Code") and (SumikaPackageTransMgt.GetBinCodeFromPackageNoInformation(PackageNoInformation) = Bin.Code) then
            CombinedShipmentLineDtl.Validate("Ready to Ship", true);
        CombinedShipmentLineDtl.Modify(true);
        //Commit();// 
    end;

    procedure CreateAndPostPackageSplitDocumentForShipment(PackageNoInformation: Record "Package No. Information"; CombinedShipmentLine: Record "Combined Shipment Line SMK"; RequiredQty: Decimal) NewPackageNo: Code[50]
    var
        PackageSplitHeader: Record "Package Split Header SMK";
        PackageSplitLine: Record "Package Split Line SMK";
        PackageSplitManagement: Codeunit "Package Split Management SMK";
    begin
        PackageSplitHeader.Init();
        PackageSplitHeader.Insert(true);
        PackageSplitHeader.Validate("Package No.", PackageNoInformation."Package No.");
        PackageSplitHeader.Validate("New Package Quantity", RequiredQty);
        PackageSplitHeader.Modify(true);
        PackageSplitManagement.CreateNewPackageSplitLineFromHeader(PackageSplitHeader, PackageSplitHeader."New Package Quantity");
        PackageSplitLine.SetRange("Document No.", PackageSplitHeader."No.");
        PackageSplitLine.FindFirst();

        PackageSplitManagement.CreateItemReclassificationJournalLinesFromPackageSplitHeader(PackageSplitHeader);
        exit(PackageSplitLine."New Package No.");
    end;

    procedure PrepareForShipment(CombinedShipmentHeader: Record "Combined Shipment Header SMK")
    var
        Bin: Record Bin;
        CombinedShipmentLine: Record "Combined Shipment Line SMK";
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl SMK";
        PackageTransferHeader: Record "Package Transfer Header SMK";
        PackageNoInformation: Record "Package No. Information";
        PostJnlLines: Boolean;
    begin
        CombinedShipmentHeader.TestField("Transferred to Sales Order", false);
        CombinedShipmentHeader.TestField("Posting Date");

        CombinedShipmentLine.SetAutoCalcFields("Line Package Quantity");
        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.SetFilter("Line Package Quantity", '<>0');
        if not CombinedShipmentLine.FindSet() then
            exit;

        repeat
            Bin.SetRange("Location Code", CombinedShipmentLine."Location Code");
            Bin.SetRange("Shipment Bin SMK", true);
            if not Bin.FindFirst() then
                Error(ShipmentBinNotDefinedErr, CombinedShipmentLine."Location Code");

            CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentLine."Document No.");
            CombinedShipmentLineDtl.SetRange("Document Line No.", CombinedShipmentLine."Line No.");
            CombinedShipmentLineDtl.SetRange("Ready to Ship", false);
            CombinedShipmentLineDtl.SetFilter("Parent Package No.", '');
            if CombinedShipmentLineDtl.FindSet() then begin
                Clear(PackageTransferHeader);
                PackageTransferHeader.Init();
                PackageTransferHeader.Insert(true);
                PackageTransferHeader.Validate("Transfer-to Code", CombinedShipmentLine."Location Code");
                PackageTransferHeader.Validate("Transfer-To Bin Code", Bin.Code);
                PackageTransferHeader.Validate("Created from Shipment", true);
                PackageTransferHeader.Modify(true);
                repeat
                    PackageTransferHeader.Validate(Barcode, CombinedShipmentLineDtl."Package No.");
                    SumikaPackageTransMgt.ProcessBarcode(PackageTransferHeader);
                    CombinedShipmentLineDtl.Validate("Ready to Ship", true);
                    PackageNoInformation.Get(CombinedShipmentLineDtl."Item No.", CombinedShipmentLineDtl."Variant Code", CombinedShipmentLineDtl."Package No.");
                    CombinedShipmentLineDtl.Validate("Old Bin Code", SumikaPackageTransMgt.GetBinCodeFromPackageNoInformation(PackageNoInformation));
                    CombinedShipmentLineDtl.Modify(true);
                    PostJnlLines := true;
                until CombinedShipmentLineDtl.Next() = 0;
            end;

            if PostJnlLines then
                SumikaPackageTransMgt.CreateItemReclassRecords(PackageTransferHeader);

            CombinedShipmentLine.Validate("Bin Code", Bin.Code);
            CombinedShipmentLine.Modify(true);
        until CombinedShipmentLine.Next() = 0;
    end;

    // [EventSubscriber(ObjectType::Codeunit, Codeunit::"Item Jnl.-Post", 'OnBeforeCode', '', false, false)]
    // local procedure OnBeforeCode(var ItemJournalLine: Record "Item Journal Line"; var HideDialog: Boolean; var SuppressCommit: Boolean; var IsHandled: Boolean);
    // begin
    //     HideDialog := true;
    // end;

    procedure TransferReadPackagesToSalesOrder(CombinedShipmentHeader: Record "Combined Shipment Header SMK")
    var
        CombinedShipmentLine: Record "Combined Shipment Line SMK";
        CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl SMK";
        SalesHeader: Record "Sales Header";
        SalesLine: Record "Sales Line";
        SalesShipmentHeader: Record "Sales Shipment Header";
        NoLinesErr: Label 'No Lines found.';
        NotReadyToShipErr: Label 'All read packages have to be Ready to Ship.';
        SuccesMsg: Label 'Combined Shipment has been transferred to Sales Order successfully.';
        ShippedMsg: Label 'Combined Shipment has been shipped succesfully.';
    begin
        CombinedShipmentHeader.TestField("Transferred to Sales Order", false);
        CombinedShipmentHeader.TestField("Posting Date");

        CombinedShipmentLine.SetAutoCalcFields("Line Package Quantity");
        CombinedShipmentLine.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLine.SetFilter("Line Package Quantity", '<>0');
        if not CombinedShipmentLine.FindSet() then
            Error(NoLinesErr);

        CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentHeader."No.");
        CombinedShipmentLineDtl.SetRange("Ready to Ship", false);
        if not CombinedShipmentLineDtl.IsEmpty() then
            Error(NotReadyToShipErr);

        repeat
            SalesLine.Get(SalesLine."Document Type"::Order, CombinedShipmentLine."Source Document No.", CombinedShipmentLine."Source Document Line No.");

            CombinedShipmentLineDtl.Reset();
            CombinedShipmentLineDtl.SetRange("Document No.", CombinedShipmentLine."Document No.");
            CombinedShipmentLineDtl.SetRange("Document Line No.", CombinedShipmentLine."Line No.");
            CombinedShipmentLineDtl.FindSet();
            repeat
                SalesLine.Validate("Qty. to Ship", SalesLine."Qty. to Ship" + CombinedShipmentLineDtl.Quantity);
                SalesLine.Modify(true);
                AssignLotAndPackagesNosToSalesLine(SalesLine, CombinedShipmentLineDtl);
            until CombinedShipmentLineDtl.Next() = 0;
        until CombinedShipmentLine.Next() = 0;

        CombinedShipmentHeader.Validate("Transferred to Sales Order", true);
        CombinedShipmentHeader.Modify(true);

        if CombinedShipmentHeader."Sales Order No." <> '' then begin
            SalesHeader.Get(SalesHeader."Document Type"::Order, CombinedShipmentHeader."Sales Order No.");
            SalesHeader."Posting Date" := CombinedShipmentHeader."Posting Date";
            SalesHeader.Ship := true;
            SalesHeader.Modify(true);
        end;

        SumikaSetup.Get();
        if SumikaSetup."Transfer&Ship Comb. Shipment" then begin
            Codeunit.Run(Codeunit::"Sales-Post", SalesHeader);

            // Find the shipments created from this combined shipment
            SalesShipmentHeader.Reset();
            SalesShipmentHeader.SetRange("Order No.", CombinedShipmentHeader."Sales Order No.");

            // Store the first shipment number in the Combined Shipment Header
            if SalesShipmentHeader.FindLast() then begin
                CombinedShipmentHeader."Posted Shipment No." := SalesShipmentHeader."No.";
                CombinedShipmentHeader.Modify(true);
            end;

            // If shipments are found, open the Posted Sales Shipments page
            if not SalesShipmentHeader.IsEmpty() then
                Page.Run(Page::"Posted Sales Shipments", SalesShipmentHeader)
            else
                Message(ShippedMsg);
        end
        else
            Message(SuccesMsg);
    end;

    procedure AssignLotAndPackagesNosToSalesLine(SalesLine: Record "Sales Line"; CombinedShipmentLineDtl: Record "CombinedShipmentLineDtl SMK")
    var
        TempReservationEntry: Record "Reservation Entry" temporary;
        CreateReservEntry: Codeunit "Create Reserv. Entry";
        ReservationStatus: Enum "Reservation Status";
    begin
        TempReservationEntry.Init();
        TempReservationEntry."Entry No." := 1;
        TempReservationEntry.Quantity := CombinedShipmentLineDtl.Quantity;
        TempReservationEntry."Lot No." := CombinedShipmentLineDtl."Lot No.";
        TempReservationEntry."Package No." := CombinedShipmentLineDtl."Package No.";
        TempReservationEntry.Insert(false);

        CreateReservEntry.CreateReservEntryFor(Database::"Sales Line", SalesLine."Document Type".AsInteger(),
                                                SalesLine."Document No.", '', 0, SalesLine."Line No.", SalesLine."Qty. per Unit of Measure",
                                                TempReservationEntry.Quantity, TempReservationEntry.Quantity * SalesLine."Qty. per Unit of Measure", TempReservationEntry);

        CreateReservEntry.CreateEntry(SalesLine."No.", SalesLine."Variant Code", SalesLine."Location Code", '',
                                        0D, 0D, 0, ReservationStatus::Surplus);

    end;

    procedure GetSourceNameFromILE(ItemLedgerEntry: Record "Item Ledger Entry"): Text[100]
    var
        Customer: Record Customer;
        Vendor: Record Vendor;
        Item: Record Item;
    begin
        case ItemLedgerEntry."Source Type" of
            ItemLedgerEntry."Source Type"::Customer:
                begin
                    if not Customer.Get(ItemLedgerEntry."Source No.") then
                        exit('');
                    exit(Customer.Name);
                end;
            ItemLedgerEntry."Source Type"::Vendor:
                begin
                    if not Vendor.Get(ItemLedgerEntry."Source No.") then
                        exit('');
                    exit(Vendor.Name);
                end;
            ItemLedgerEntry."Source Type"::Item:
                begin
                    if not Item.Get(ItemLedgerEntry."Source No.") then
                        exit('');
                    exit(Item.Description);
                end;
        end;
    end;

    [EventSubscriber(ObjectType::Table, Database::"Sales Line", OnAfterAssignItemValues, '', false, false)]
    local procedure "Sales Line_OnAfterAssignItemValues"(var SalesLine: Record "Sales Line"; Item: Record Item; SalesHeader: Record "Sales Header"; var xSalesLine: Record "Sales Line"; CurrentFieldNo: Integer)
    begin
        SalesLine."Packaging Type SMK" := Item."Packaging Type SMK";
    end;


    procedure OnAfterValidateCompletedOnSalesHeader(var SalesHeader: Record "Sales Header")
    var
        SalesLine: Record "Sales Line";
        ConfirmManagement: Codeunit "Confirm Management";
        ConfirmQst: Label 'There is at least one line that is not completely shipped. Do you want to continue?';
    begin
        if not SalesHeader."Completed SMK" then
            exit;

        if SalesHeader."Completely Shipped" then
            exit;

        if not ConfirmManagement.GetResponseOrDefault(ConfirmQst, true) then
            exit;

        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetRange("Completely Shipped", false);
        if SalesLine.FindSet(true) then
            if SalesHeader.Status = SalesHeader.Status::Released then begin
                SalesHeader.Status := SalesHeader.Status::Open;
                SalesHeader.Modify(true);
            end;
        repeat
            SalesLine.Validate(Quantity, SalesLine."Quantity Shipped");
            SalesLine.Modify(true);
        until SalesLine.Next() = 0;

        SalesHeader.Status := SalesHeader.Status::Released;
        SalesHeader.Modify(true);

        SalesLine.Reset();
        SalesLine.SetRange("Document Type", SalesHeader."Document Type");
        SalesLine.SetRange("Document No.", SalesHeader."No.");
        SalesLine.SetFilter("Quantity Shipped", '<>%1', 0);
        if SalesLine.IsEmpty() then
            SalesHeader.Delete(true);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Sales-Post (Yes/No)", OnAfterPost, '', false, false)]
    local procedure "Sales-Post (Yes/No)_OnAfterPost"(var SalesHeader: Record "Sales Header"; PostAndSend: Boolean)
    begin
        BusinessEventHandler.OnAfterPostSalesHeaderBe(SalesHeader."Last Shipping No.", SalesHeader."No.", SalesHeader."Location Code", SalesHeader."Sell-to Customer No.", SalesHeader."Sell-to Customer Name", SalesHeader."External Document No.");
    end;



    var
        SumikaSetup: Record "Sumika Setup SMK";
        SumikaPackageTransMgt: Codeunit "Sumika Package Trans. Mgt. SMK";
        BusinessEventHandler: Codeunit "Business Event Handler SMK";
        ShipmentBinNotDefinedErr: Label 'You have to define Shipment Bin for Location Code: %1', Comment = '%1 is Location Code';
}