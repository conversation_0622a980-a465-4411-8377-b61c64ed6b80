table 60002 "CombinedReceivingLineDtl SMK"
{
    Caption = 'Combined Receiving Line Detail';
    DrillDownPageId = "Combined Receiving LineDetail";
    LookupPageId = "Combined Receiving LineDetail";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(4; "Purchase Order No."; Code[20])
        {
            Caption = 'Purchase Order No.';
            ToolTip = 'Specifies the value of the Purchase Order No. field.';
        }
        field(5; "Purchase Order Line No."; Integer)
        {
            Caption = 'Purchase Order Line No.';
            ToolTip = 'Specifies the value of the Purchase Order Line No. field.';
        }
        field(6; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(7; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(8; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(9; "Qty. to Receive"; Decimal)
        {
            Caption = 'Quantity-to Receive';
            ToolTip = 'Specifies the value of the Quantity-to Receive field.';
        }
        field(10; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(13; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            AllowInCustomizations = Always;
        }
        field(14; Received; Boolean)
        {
            Caption = 'Received';
            ToolTip = 'Specifies the value of the Received field.';
        }
        field(15; "Buy-From Vendor No."; Code[20])
        {
            Caption = 'Buy-From Vendor No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Combined Receiving Header SMK"."Vendor No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Buy-From Vendor No. field.';
        }
        field(16; "Buy-from Vendor Name"; Text[100])
        {
            Caption = 'Buy-from Vendor Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Combined Receiving Header SMK"."Vendor Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Buy-from Vendor Name field.';
        }
        field(17; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(18; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            ToolTip = 'Specifies the value of the Bin Code field.';
        }
        field(19; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(20; "Parent Package No."; Code[50])
        {
            Caption = 'Parent Package No.';
            ToolTip = 'Specifies the value of the Parent Package No. field.';
        }
        field(21; "Vendor Lot No."; Code[50])
        {
            Caption = 'Vendor Lot No.';
            ToolTip = 'Specifies the value of the Vendor Lot No. field.';

            trigger OnValidate()
            var
                PackageNoInfo: Record "Package No. Information";
            begin
                if PackageNoInfo.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then begin
                    PackageNoInfo."Vendor Lot No. SMK" := "Vendor Lot No.";
                    PackageNoInfo.Modify(true);
                end;
            end;
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
            SumIndexFields = "Qty. to Receive";
        }
    }
    trigger OnInsert()
    var
        CombinedReceivingLineDetail: Record "CombinedReceivingLineDtl SMK";
    begin
        CombinedReceivingLineDetail.SetRange("Document No.", Rec."Document No.");
        CombinedReceivingLineDetail.SetRange("Document Line No.", Rec."Document Line No.");
        if CombinedReceivingLineDetail.FindLast() then
            Rec."Line No." := CombinedReceivingLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}
