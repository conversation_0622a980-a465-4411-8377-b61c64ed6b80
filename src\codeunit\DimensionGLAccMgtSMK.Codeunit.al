codeunit 60005 "Dimension - G/L Acc. Mgt. SMK"
{
    procedure InsertGLAccountLineToPurchaseLine(PurchaseHeader: Record "Purchase Header"; GLAccountNo: Code[20]; Dim1: Code[20]; Dim2: Code[20])
    var
        PurchaseLine: Record "Purchase Line";
        LineNo: Integer;
    begin
        PurchaseLine.SetRange("Document Type", PurchaseHeader."Document Type");
        PurchaseLine.SetRange("Document No.", PurchaseHeader."No.");
        if PurchaseLine.FindLast() then
            LineNo := PurchaseLine."Line No." + 10000
        else
            LineNo := 10000;

        PurchaseLine.Init();
        PurchaseLine."Document Type" := PurchaseHeader."Document Type";
        PurchaseLine."Document No." := PurchaseHeader."No.";
        PurchaseLine."Line No." := LineNo;
        PurchaseLine.Insert(true);
        PurchaseLine.Validate(Type, PurchaseLine.Type::"G/L Account");
        PurchaseLine.Validate("No.", GLAccountNo);
        PurchaseLine.Validate("Shortcut Dimension 1 Code", Dim1);
        PurchaseLine.Validate("Shortcut Dimension 2 Code", Dim2);
        PurchaseLine.Validate("Lock No. Field SMK", true);
        PurchaseLine.Modify(true);
    end;

    procedure InsertGLAccountLineToGeneralJournalLine(GenJournalTemplateName: Code[10]; GenJournalBatchName: Code[10]; GLAccountNo: Code[20]; Dim1: Code[20]; Dim2: Code[20])
    var
        GenJournalLine: Record "Gen. Journal Line";
        LastGenJournalLine: Record "Gen. Journal Line";
        LineNo: Integer;
    begin
        LastGenJournalLine.SetRange("Journal Template Name", GenJournalTemplateName);
        LastGenJournalLine.SetRange("Journal Batch Name", GenJournalBatchName);
        if LastGenJournalLine.FindLast() then
            LineNo := LastGenJournalLine."Line No." + 10000
        else
            LineNo := 10000;

        GenJournalLine.Init();
        GenJournalLine."Journal Template Name" := GenJournalTemplateName;
        GenJournalLine."Journal Batch Name" := GenJournalBatchName;
        GenJournalLine."Line No." := LineNo;
        GenJournalLine.SetUpNewLine(LastGenJournalLine, 0, false);
        GenJournalLine.Insert(true);
        GenJournalLine.Validate("Account Type", GenJournalLine."Account Type"::"G/L Account");
        GenJournalLine.Validate("Account No.", GLAccountNo);
        GenJournalLine.Validate("Shortcut Dimension 1 Code", Dim1);
        GenJournalLine.Validate("Shortcut Dimension 2 Code", Dim2);
        GenJournalLine.Modify(true);
    end;


    procedure GetGLAccountNoFromDimensions(Dim1: Code[20]; Dim2: Code[20]): Code[20]
    var
        DimensionGLAccSetup: Record "Dimension - G/L Acc. Setup SMK";
        GeneralLedgerSetup: Record "General Ledger Setup";
        NoGLAccountMsg: Label 'There is no Dimension - G/L Account Setup for selected dimensions.';
    begin
        GeneralLedgerSetup.Get();

        GeneralLedgerSetup.TestField("Global Dimension 1 Code");
        GeneralLedgerSetup.TestField("Global Dimension 2 Code");

        if (Dim1 = '') or (Dim2 = '') then
            exit;

        if DimensionGLAccSetup.Get(GeneralLedgerSetup."Global Dimension 1 Code", Dim1, GeneralLedgerSetup."Global Dimension 2 Code", Dim2) then
            exit(DimensionGLAccSetup."G/L Account No.");

        Message(NoGLAccountMsg);
        exit('');

    end;

    [EventSubscriber(ObjectType::Table, Database::"Purchase Line", OnBeforeValidateNo, '', false, false)]
    local procedure OnBeforeValidateNo(var PurchaseLine: Record "Purchase Line"; xPurchaseLine: Record "Purchase Line"; CurrentFieldNo: Integer; var IsHandled: Boolean)
    var
        CanNotChangeNoErr: Label 'You can not change "No." value if you have dimensions selected.';
    begin
        if PurchaseLine.IsTemporary() then
            exit;

        if PurchaseLine.Type <> PurchaseLine.Type::"G/L Account" then
            exit;

        if (PurchaseLine."Shortcut Dimension 1 Code" = '') or (PurchaseLine."Shortcut Dimension 2 Code" = '') then
            exit;

        if not PurchaseLine."Lock No. Field SMK" then
            exit;

        if PurchaseLine."No." <> xPurchaseLine."No." then
            Error(CanNotChangeNoErr);
    end;

    #region
    // [EventSubscriber(ObjectType::Table, Database::"Purchase Line", 'OnAfterValidateShortcutDimCode', '', false, false)]
    // local procedure OnAfterValidateShortcutDimCode(var PurchaseLine: Record "Purchase Line"; var xPurchaseLine: Record "Purchase Line"; FieldNumber: Integer; var ShortcutDimCode: Code[20]);
    // var
    //     DimensionGLAccSetup: Record "Dimension - G/L Acc. Setup SMK";
    //     GeneralLedgerSetup: Record "General Ledger Setup";

    //     Dimension1Code: Code[20];
    //     Dimension2Code: Code[20];
    // begin
    //     GeneralLedgerSetup.Get();

    //     GeneralLedgerSetup.TestField("Global Dimension 1 Code");
    //     GeneralLedgerSetup.TestField("Global Dimension 2 Code");



    //     // if PurchaseLine."No." <> '' then
    //     //     exit;

    //     Dimension1Code := PurchaseLine."Shortcut Dimension 1 Code";
    //     Dimension2Code := PurchaseLine."Shortcut Dimension 2 Code";

    //     if DimensionGLAccSetup.Get(GeneralLedgerSetup."Global Dimension 1 Code", PurchaseLine."Shortcut Dimension 1 Code", GeneralLedgerSetup."Global Dimension 2 Code", PurchaseLine."Shortcut Dimension 2 Code") then begin
    //         PurchaseLine.Validate("Lock No. Field SMK", false);
    //         PurchaseLine.Validate("No.", DimensionGLAccSetup."G/L Account No.");
    //         PurchaseLine.Validate("Shortcut Dimension 1 Code", Dimension1Code);
    //         PurchaseLine.Validate("Shortcut Dimension 2 Code", Dimension2Code);
    //         PurchaseLine.Validate("Lock No. Field SMK", true);
    //     end
    //     else
    //         Message(NoGLAccountMsg);
    // end;
    #endregion
}