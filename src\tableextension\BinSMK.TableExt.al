tableextension 60020 "Bin SMK" extends Bin
{
    fields
    {
        field(60000; "Shipment Bin SMK"; Boolean)
        {
            Caption = 'Shipment Bin';
            ToolTip = 'Specifies the value of the Shipment Bin field.';
            trigger OnValidate()
            var
                Bin: Record Bin;
                OnlyOneShipmentBinErr: Label 'There can be only one Shipment Bin for each Location.';
            begin
                Bin.SetRange("Location Code", Rec."Location Code");
                Bin.SetRange("Shipment Bin SMK", true);
                if not Bin.IsEmpty() then
                    Error(OnlyOneShipmentBinErr);
            end;
        }
    }
}