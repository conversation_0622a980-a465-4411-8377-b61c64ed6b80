report 60005 "Items by Lot And Bin SMK"
{
    ApplicationArea = All;
    Caption = 'Items by Lot And Bin';
    UsageCategory = ReportsAndAnalysis;
    dataset
    {
        dataitem(WarehouseEntry; "Warehouse Entry")
        {
            column(ItemNo; "Item No.")
            {
            }
            column(Description; Description)
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(BinCode; "Bin Code")
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(ItemDescription; SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode(WarehouseEntry."Item No.", ''))
            {
            }
            column(PackagingTypeCode; SumikaBasicFunctions.GetPackagingTypeCodeFromItemNo(WarehouseEntry."Item No."))
            {
            }
        }
    }
    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
}