tableextension 60008 "Lot No. Information SMK" extends "Lot No. Information"
{
    fields
    {
        field(60000; "Company No. SMK"; Code[20])
        {
            Caption = 'Company No. No.';
            ToolTip = 'Specifies the value of the Company No. No. field.';
        }
        field(60001; "Company Name SMK"; Text[100])
        {
            Caption = 'Company Name';
            ToolTip = 'Specifies the value of the Company Name Name field.';
        }
        field(60002; "Vendor Lot No. SMK"; Code[50])
        {
            Caption = 'Vendor Lot No.';
            ToolTip = 'Specifies the value of the Vendor Lot No. field.';
        }
        field(60003; "Packaging Type Code SMK"; Code[100])
        {
            Caption = 'Packaging Type Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("Item No.")));
            ToolTip = 'Specifies the value of the Packaging Type Code field.';
        }
    }
}