page 60037 "Item Attribute Value Mapping"
{
    ApplicationArea = All;
    Caption = 'Item Attribute Value Mapping';
    PageType = List;
    SourceTable = "Item Attribute Value Mapping";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Table ID"; Rec."Table ID")
                {
                    ToolTip = 'Specifies the table of the record to which the attribute applies (for example Database::Item or Database::"Item Category").';
                }
                field("No."; Rec."No.")
                {
                    ToolTip = 'Specifies the key of the record to which the attribute applies (the record type is specified by "Table ID").';
                }
                field("Description SMK"; Rec."Description SMK")
                {
                }
                field("Item Attribute ID"; Rec."Item Attribute ID")
                {
                    ToolTip = 'Specifies the value of the Item Attribute ID field.';
                }
                field("Item Attiribute Name SMK"; Rec."Item Attiribute Name SMK")
                {
                }
                field("Item Attribute Value ID"; Rec."Item Attribute Value ID")
                {
                    ToolTip = 'Specifies the value of the Item Attribute Value ID field.';
                }
                field("Item Attribute Value Name SMK"; Rec."Item Attribute Value Name SMK")
                {
                }
            }
        }
    }
}