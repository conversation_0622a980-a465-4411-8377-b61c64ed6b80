table 60023 "Sumika E-Mail Setup SMK"
{
    Caption = 'Sumika E-Mail Setup';
    DataClassification = CustomerContent;
    DrillDownPageId = "Sumika E-Mail Setup SMK";
    LookupPageId = "Sumika E-Mail Setup SMK";

    fields
    {
        field(1; "Entry No."; Integer)
        {
            Caption = 'Entry No.';
            ToolTip = 'Specifies the value of the Entry No. field.';
        }
        field(2; "User ID"; Code[50])
        {
            Caption = 'User ID';
            TableRelation = "User Setup"."User ID";
            ToolTip = 'Specifies the value of the User ID field.';
        }
        field(3; "Item No. Filter"; Text[250])
        {
            Caption = 'Item No. Filter';
            ToolTip = 'Specifies the value of the Item No. Filter field.';
        }
        field(4; "Location Code Filter"; Text[250])
        {
            Caption = 'Location Code Filter';
            ToolTip = 'Specifies the value of the Location Code field.';
            //TableRelation = "Location";
        }
        field(5; "E-Mail"; Text[100])
        {
            Caption = 'E-Mail';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("User Setup"."E-Mail" where("User ID" = field("User ID")));
            ToolTip = 'Specifies the value of the E-Mail field.';
        }
    }
    keys
    {
        key(PK; "Entry No.")
        {
            Clustered = true;
        }
        key(SK; "User ID", "Item No. Filter", "Location Code Filter")
        {
            Unique = true;
        }
    }
    trigger OnInsert()
    var
        SumikaEMailSetup: Record "Sumika E-Mail Setup SMK";
    begin
        if SumikaEMailSetup.FindLast() then
            "Entry No." := SumikaEMailSetup."Entry No." + 1
        else
            "Entry No." := 1;
    end;
}