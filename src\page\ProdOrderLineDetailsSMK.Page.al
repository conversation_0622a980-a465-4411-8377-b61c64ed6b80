page 60020 "Prod. Order Line Details SMK"
{
    ApplicationArea = All;
    Caption = 'Prod. Order Line Details';
    PageType = List;
    SourceTable = "Prod. Order Line Detail SMK";
    UsageCategory = Lists;

    SourceTableView = sorting(SystemCreatedAt) order(descending);
    InsertAllowed = false;
    DeleteAllowed = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    Editable = false;
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    Editable = false;
                }
                field("Item Description"; Rec."Item Description")
                {
                    Editable = false;
                }
                field("Location Code"; Rec."Location Code")
                {
                    Editable = not Rec.Posted;
                }
                field("Bin Code"; Rec."Bin Code")
                {
                    Editable = not Rec.Posted;
                }
                field(Quantity; Rec.Quantity)
                {
                    Editable = not Rec.Posted;
                }
                // field("Label to Print Count"; Rec."Label to Print Count")
                // {
                // }
                field("Lot No."; Rec."Lot No.")
                {
                    Editable = false;
                }
                field("Package No."; Rec."Package No.")
                {
                    Editable = false;
                    trigger OnDrillDown()
                    var
                        PackageNoInformation: Record "Package No. Information";
                    begin
                        PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.");
                        Page.Run(Page::"Package No. Information Card", PackageNoInformation);
                    end;
                }
                field("Package Order No."; Rec."Package Order No.")
                {
                }
                field("Quality Control Status"; Rec."Quality Control Status")
                {
                }

                field(Posted; Rec.Posted)
                {
                    Editable = false;
                }
                field("Parent Package No."; Rec."Parent Package No.")
                {
                    Editable = false;
                }
                // field(ParentPackageQuantity; SumikaProductionMgt.CalculateChildPackageQuantity(Rec."Package No."))
                // {
                //     Caption = 'Parent Package Quantity';
                //     ToolTip = 'Specifies the value of the Parent Package Quantity field.';
                // }
                field("Child Package Count"; Rec."Child Package Count")
                {
                    trigger OnDrillDown()
                    // var
                    //     PackageNoInformation: Record "Package No. Information";
                    begin
                        SumikaProductionMgt.OnAfterDrillDownChieldPackageCount_ProdOrderLineDetails(Rec);
                    end;
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(PostSelectedLines)
            {
                ApplicationArea = All;
                Caption = 'Post Selected Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = PostOrder;
                ToolTip = 'Executes the Post Selected Lines action.';
                PromotedOnly = true;

                trigger OnAction()
                var
                    ProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
                //ProdOrderLineDetail2: Record "Prod. Order Line Detail SMK";
                begin
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    ProdOrderLineDetail.FindSet();
                    repeat
                        // if (ProdOrderLineDetail.Quantity = 0) and (ProdOrderLineDetail."Parent Package No." = '') then begin
                        //     ProdOrderLineDetail2.SetRange("Parent Package No.", ProdOrderLineDetail."Package No.");
                        //     if ProdOrderLineDetail2.FindSet() then
                        //         repeat
                        //             SumikaProductionMgt.CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail2);
                        //         until ProdOrderLineDetail2.Next() = 0;
                        //     ProdOrderLineDetail.Posted := true;
                        //     ProdOrderLineDetail.Modify(true);
                        // end
                        // else
                        ProdOrderLineDetail.TestField("Parent Package No.", '');
                        SumikaProductionMgt.CreateOutputJournalsFromProdOrderLineDetail(ProdOrderLineDetail);
                    until ProdOrderLineDetail.Next() = 0;
                end;
            }
            action(Print)
            {
                ApplicationArea = All;
                Caption = 'Print';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print action.';

                trigger OnAction()
                var
                    ProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
                    ProdOrderLineDetail2: Record "Prod. Order Line Detail SMK";
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(ProdOrderLineDetail);
                    if ProdOrderLineDetail.FindSet() then
                        repeat
                            //if (ProdOrderLineDetail.Quantity = 0) and (ProdOrderLineDetail."Parent Package No." = '') then begin
                            // if (ProdOrderLineDetail."Parent Package No." = '') then begin
                            ProdOrderLineDetail2.SetRange("Parent Package No.", ProdOrderLineDetail."Package No.");

                            PackageNoInformation.Get(ProdOrderLineDetail."Item No.", ProdOrderLineDetail."Variant Code", ProdOrderLineDetail."Package No.");
                            PackageNoInformation.Mark(true);
                            if ProdOrderLineDetail2.FindSet() then //begin
                                repeat
                                    PackageNoInformation.Get(ProdOrderLineDetail2."Item No.", ProdOrderLineDetail2."Variant Code", ProdOrderLineDetail2."Package No.");
                                    PackageNoInformation.Mark(true);
                                until ProdOrderLineDetail2.Next() = 0;
                        // end
                        // end
                        // else begin
                        //     PackageNoInformation.Get(ProdOrderLineDetail."Item No.", ProdOrderLineDetail."Variant Code", ProdOrderLineDetail."Package No.");
                        //     PackageNoInformation.Mark(true);
                        // end;
                        until ProdOrderLineDetail.Next() = 0;

                    PackageNoInformation.MarkedOnly(true);

                    //Message('%1', PackageNoInformation.Count);

                    Report.Run(Report::"Package Label SMK", true, true, PackageNoInformation);
                end;
            }
            action(Comment)
            {
                Caption = 'Comments';
                Image = ViewComments;
                ApplicationArea = All;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                RunObject = page "Item Tracking Comments";
                RunPageLink = "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Serial/Lot No." = field("Package No.");
                ToolTip = 'View or add comments for the record.';
            }
        }
    }

    trigger OnDeleteRecord(): Boolean
    var
        DeletionNotAllowedErr: Label 'You cannot delete posted records.';
    begin
        if Rec.Posted then
            Error(DeletionNotAllowedErr);
    end;

    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}