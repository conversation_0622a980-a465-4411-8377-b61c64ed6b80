page 60006 "Production Parameters SMK"
{
    ApplicationArea = All;
    Caption = 'Production Parameters';
    PageType = List;
    SourceTable = "Production Parameter SMK";
    UsageCategory = Administration;
    DelayedInsert = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Type; Rec."Type")
                {
                }

                field("Machine No."; Rec."Machine No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Barrel Temperature (°C)"; Rec."Barrel Temperature (°C)")
                {
                }
                field("Screw Speed (rpm)"; Rec."Screw Speed (rpm)")
                {
                    ApplicationArea = Manufacturing;
                }
                field("Torque (%)"; Rec."Torque (%)")
                {
                }
                field("Pelletization Speed (m/min)"; Rec."Pelletization Speed (m/min)")
                {
                    BlankZero = true;
                }
                field("Pellet Temperature (°C)"; Rec."Pellet Temperature (°C)")
                {
                    BlankZero = true;
                }
                field("Vaccuum (bar)"; Rec."Vaccuum (bar)")
                {
                }
                field("Output (kg/saat)"; Rec."Output (kg/saat)")
                {
                }
                field("Bath Temperature"; Rec."Bath Temperature")
                {
                }
                field("Filter Diameter (Mesh)"; Rec."Filter Diameter (Mesh)")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(TemperatureParameters)
            {
                ApplicationArea = All;
                Caption = 'Temperature Parameters';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                Image = Percentage;
                ToolTip = 'Executes the Temperature Parameters action.';

                trigger OnAction()
                var
                    TemperatureParameter: Record "Temperature Parameter SMK";
                begin
                    TemperatureParameter.SetRange("Machine No.", Rec."Machine No.");
                    TemperatureParameter.SetRange("Item No.", Rec."Item No.");

                    Page.Run(Page::"Temperature Parameters SMK", TemperatureParameter);
                end;
            }
            action(Copy)
            {
                ApplicationArea = All;
                Caption = 'Copy';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Copy;
                ToolTip = 'Executes the Copy action.';

                trigger OnAction()
                begin
                    Page.RunModal(Page::"Copy Prod. Parameter Dialog", Rec);
                    CurrPage.Update();
                end;
            }
        }
    }
}