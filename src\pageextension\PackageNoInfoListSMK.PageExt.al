pageextension 60011 "Package No. Info. List SMK" extends "Package No. Information List"
{

    layout
    {
        addlast(Control1)
        {
            field("Vendor Lot No. SMK"; Rec."Vendor Lot No. SMK")
            {
                ApplicationArea = All;
                //Visible = false;
            }
            field("Certificate Number SMK"; Rec."Certificate Number")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Certificate Number field.';
            }
            field("Expiration Date SMK"; SumikaBasicFunctions.GetExpirationDateFromLotNo(Rec."Lot No. SMK"))
            {
                Caption = 'Expiration Date';
                ToolTip = 'Specifies the value of the Expiration Date field.';
                ApplicationArea = All;
            }
            field("Old Package No. SMK"; Rec."Old Package No. SMK")
            {
                ApplicationArea = All;
            }
            field("Inventory Entry Date SMK"; Rec."Inventory Entry Date SMK")
            {
                ApplicationArea = All;
            }
            field("Lot No. SMK"; Rec."Lot No. SMK")
            {
                ApplicationArea = All;
            }
            field("Comment Count SMK"; Rec."Comment Count SMK")
            {
                ApplicationArea = All;
                trigger OnDrillDown()
                var
                    ItemTrackingComment: Record "Item Tracking Comment";
                    ItemTrackingComments: Page "Item Tracking Comments";
                begin
                    ItemTrackingComment.SetRange("Item No.", Rec."Item No.");
                    ItemTrackingComment.SetRange("Variant Code", Rec."Variant Code");
                    ItemTrackingComment.SetRange("Serial/Lot No.", Rec."Package No.");
                    ItemTrackingComments.SetTableView(ItemTrackingComment);
                    ItemTrackingComments.RunModal();
                end;
            }
            field("Package Order No. SMK"; Rec."Package Order No. SMK")
            {
                ApplicationArea = All;
            }
            field("Shelf Life SMK"; Rec."Shelf Life SMK")
            {
                ApplicationArea = All;
            }
            field("Production Order No. SMK"; Rec."Production Order No. SMK")
            {
                ApplicationArea = All;
            }
            field("Quality Control Status SMK"; Rec."Quality Control Status SMK")
            {
                ApplicationArea = All;
            }
            field("SystemCreatedAt SMK"; Rec.SystemCreatedAt)
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the SystemCreatedAt field.';
            }
            field("Parent Package No. SMK"; Rec."Parent Package No. SMK")
            {
                ApplicationArea = All;
                //Visible = false;
            }
            field("Location Code SMK"; Rec."Location Code SMK")
            {
                ApplicationArea = All;
                //Visible = false;
            }
            field("Bin Code SMK"; SumikaPackageTransMgt.GetBinCodeFromPackageNoInformation(Rec))
            {
                Caption = 'Bin Code';
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Bin Code field.';
                //Visible = false;
            }
            field("Packaging Type Code SMK"; Rec."Packaging Type Code SMK")
            {
                ApplicationArea = All;
            }
            // field("Parent Package Inventory SMK"; Rec."Parent Package Inventory SMK")
            // {
            //     ApplicationArea = All;
            //     ToolTip = 'Specifies the value of the Parent Package Inventory field.';
            //     Visible = ParentPackageInventoryVisible;
            //     Enabled = ParentPackageInventoryVisible;
            // }
        }
    }
    actions
    {
        addfirst("&Package")
        {
            action("PackageLabel SMK")
            {
                ApplicationArea = All;
                Caption = 'Print Package Label';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print Package Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                begin
                    CurrPage.SetSelectionFilter(PackageNoInformation);
                    Report.Run(Report::"Package Label SMK", true, true, PackageNoInformation);
                end;
            }
            action("ShowHideZeroInventory SMK")
            {
                ApplicationArea = All;
                Caption = 'Show/Hide Zero Inventory';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = InventoryCalculation;
                ToolTip = 'Executes the Show/Hide Zero Inventory action.';

                trigger OnAction()
                begin
                    if Rec.GetFilter("Inventory Exist SMK") <> '' then
                        Rec.SetRange("Inventory Exist SMK")
                    else
                        Rec.SetFilter("Inventory Exist SMK", 'true');
                end;
            }
        }
    }
    trigger OnOpenPage()
    begin
        //Rec.SetFilter(Inventory, '<>0');
        // Rec.SetCurrentKey(Inventory);
        // Rec.Ascending(false);
        Rec.SetRange("Inventory Exist SMK", true);
    end;

    var
        SumikaPackageTransMgt: Codeunit "Sumika Package Trans. Mgt. SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
    //ParentPackageInventoryVisible: Boolean;
}