page 60033 "Package Split List SMK"
{
    ApplicationArea = All;
    Caption = 'Package Split List';
    PageType = List;
    SourceTable = "Package Split Header SMK";
    UsageCategory = Lists;
    CardPageId = "Package Split SMK";

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("No."; Rec."No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Completed; Rec.Completed)
                {
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
        }
    }
}