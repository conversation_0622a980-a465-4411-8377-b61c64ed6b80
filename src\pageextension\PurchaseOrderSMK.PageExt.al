pageextension 60013 "Purchase Order SMK" extends "Purchase Order"
{
    layout
    {
        addlast(General)
        {
            field("Completed SMK"; Rec."Completed SMK")
            {
                ApplicationArea = All;
            }
            field("Created By SMK"; Rec."Created By SMK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addfirst("F&unctions")
        {
            action("NewGLAccountLine SMK")
            {
                ApplicationArea = All;
                Caption = 'New G/L Account Line';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = GL;
                ToolTip = 'Executes the New G/L Account Line action.';

                trigger OnAction()
                var
                    NewGLAccountLine: Page "New G/L Account Line SMK";
                begin
                    NewGLAccountLine.GetPurchaseHeader(Rec);
                    NewGLAccountLine.RunModal();
                end;
            }
            action("CreateCombinedReceivingDocument SMK")
            {
                ApplicationArea = All;
                Caption = 'Create Combined Receiving Document', Comment = 'TRK="Teslim Alma Belgesi Oluştur"';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReceiveLoaner;
                ToolTip = 'Executes the Create Combined Receiving Document action.';
                trigger OnAction()
                begin
                    SumikaPurchaseManagement.CreateCombinedReceivingFromPurchaseOrder(Rec);
                end;
            }
        }
    }
    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";

}