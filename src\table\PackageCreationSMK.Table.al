table 60004 "Package Creation SMK"
{
    Caption = 'Package Creation';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(2; Type; Enum "Package Creation Type SMK")
        {
            Caption = 'Type';
            InitValue = Multiple;
            ToolTip = 'Specifies the value of the Type field.';
            trigger OnValidate()
            begin
                SumikaPurchaseManagement.UpdatePackageCreationQuantities(Rec, Rec.FieldNo("Entry Type"));
            end;
        }
        field(3; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(4; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(5; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(6; "Package Quantity"; Decimal)
        {
            Caption = 'Package Quantity';
            ToolTip = 'Specifies the value of the Package Quantity field.';
            trigger OnValidate()
            begin
                SumikaPurchaseManagement.UpdatePackageCreationQuantities(Rec, Rec.FieldNo("Package Quantity"));
            end;
        }
        field(7; "Number of Packages"; Integer)
        {
            Caption = 'Number of Packages';
            ToolTip = 'Specifies the value of the Number of Packages field.';
        }
        field(8; "Qty. to Receive"; Decimal)
        {
            Caption = 'Qty. to Receive';
            ToolTip = 'Specifies the value of the Qty. to Receive field.';
            trigger OnValidate()
            begin
                SumikaPurchaseManagement.UpdatePackageCreationQuantities(Rec, Rec.FieldNo("Qty. to Receive"));
            end;
        }
        field(9; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(10; "Source Document No."; Code[20])
        {
            Caption = 'Source Document No.';
            AllowInCustomizations = Always;
        }
        field(11; "Source Document Line No."; Integer)
        {
            Caption = 'Source Document Line No.';
            AllowInCustomizations = Always;
        }
        field(12; "Entry Type"; Enum "Quality Control Type SMK")
        {
            Caption = 'Entry Type';
            AllowInCustomizations = Always;
        }
        field(13; "Parent Package No."; Code[50])
        {
            Caption = 'Parent Package No.';
            TableRelation = "Package No. Information"."Package No." where("Parent Package No. SMK" = filter(''));
            ToolTip = 'Specifies the value of the Parent Package No. field.';
            //ValidateTableRelation = false;
        }
    }
    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
    var
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
}