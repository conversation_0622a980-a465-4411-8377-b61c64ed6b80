table 60010 "Item Quality Control Spec. SMK"
{
    Caption = 'Item Quality Control Spec.';
    DataClassification = CustomerContent;
    DrillDownPageId = "Item Quality Control Spec. SMK";
    LookupPageId = "Item Quality Control Spec. SMK";

    fields
    {
        field(1; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if Item.Get("Item No.") then
                    Rec.Validate("Item Description", Item.Description)
                else
                    Rec.Validate("Item Description", '');
            end;
        }
        field(2; Type; Enum "Quality Control Type SMK")
        {
            Caption = 'Type';
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(3; "Specification Code"; Code[10])
        {
            Caption = 'Specification Code';
            TableRelation = "Quality Control Spec. SMK".Code;
            ToolTip = 'Specifies the value of the Specification Code field.';
            trigger OnValidate()
            begin
                SumikaQualityCtrlMgt.PopualteItemQCSpecFromSpecCode(Rec);
            end;
        }
        field(4; "Specification Description"; Text[100])
        {
            Caption = 'Specification Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Specification Description field.';
        }
        field(5; "Min Value"; Decimal)
        {
            Caption = 'Min Value';
            ToolTip = 'Specifies the value of the Min Value field.';
        }
        field(6; "Max Value"; Decimal)
        {
            Caption = 'Max Value';
            ToolTip = 'Specifies the value of the Max Value field.';
        }
        field(7; "Exact Value"; Decimal)
        {
            Caption = 'Exact Value';
            ToolTip = 'Specifies the value of the Exact Value field.';
        }
        field(8; "Selection Value"; Enum "Quality Control Selection SMK")
        {
            Caption = 'Selection Value';
            ToolTip = 'Specifies the value of the Text Value field.';
        }
        field(9; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(10; Standard; Text[30])
        {
            Caption = 'Standard';
            Editable = false;
            ToolTip = 'Specifies the value of the Standard field.';
        }
        field(11; "Specification Reference"; Enum "Q.C. Spec. Reference Type SMK")
        {
            Caption = 'Specification Reference';
            ToolTip = 'Specifies the value of the Specification Reference field.';
        }
        field(12; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
    }
    keys
    {
        key(PK; "Item No.", "Type", "Specification Code")
        {
            Clustered = true;
        }
    }
    var
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
}