tableextension 60005 "Purchase Line SMK" extends "Purchase Line"
{
    fields
    {
        field(60005; "Status SMK"; Enum "Purchase Document Status")
        {
            Caption = 'Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Purchase Header".Status where("Document Type" = field("Document Type"), "No." = field("Document No.")));
            AllowInCustomizations = Always;
        }
        field(60002; "Lock No. Field SMK"; Boolean)
        {
            Caption = 'Lock No. Field';
            AllowInCustomizations = Always;
        }
        field(60004; "Packaging Type Code SMK"; Code[100])
        {
            Caption = 'Packaging Type Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("No.")));
            ToolTip = 'Specifies the value of the Packaging Type Code field.';
        }
    }
}