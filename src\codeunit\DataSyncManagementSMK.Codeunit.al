codeunit 60007 "Data Sync. Management SMK"
{
    #region Copy Item
    procedure CopyItemToSelectedCompany(SourceItem: Record Item)
    var
        FailedErr: Label 'Item No.: %1 not copied to %2', Comment = '%1=Item."No."; %2=TargetCompanyNameLbl';
        SuccesMsg: Label 'Item No.: %1 copied to %2', Comment = '%1=Item."No."; %2=TargetCompanyNameLbl';

    begin
        CompanySelection();

        OK := StartSession(SessionID, Codeunit::"Copy Item SMK", TargetCompanyName, SourceItem);

        CheckAndDisplayNotFoundErrorsForCopyItem(SourceItem);

        if OK then
            Message(SuccesMsg, SourceItem."No.", TargetCompanyName)
        else
            Error(FailedErr, SourceItem."No.", TargetCompanyName);
    end;

    procedure CheckAndDisplayNotFoundErrorsForCopyItem(SourceItem: Record Item)
    var
        GenProductPostingGroup: Record "Gen. Product Posting Group";
        InventoryPostingGroup: Record "Inventory Posting Group";
        ItemCategory: Record "Item Category";
        PackagingType: Record "Packaging Type SMK";
        ProductionBOMHeader: Record "Production BOM Header";
        VATProductPostingGroup: Record "VAT Product Posting Group";
    begin
        PackagingType.ChangeCompany(TargetCompanyName);
        ItemCategory.ChangeCompany(TargetCompanyName);
        GenProductPostingGroup.ChangeCompany(TargetCompanyName);
        VATProductPostingGroup.ChangeCompany(TargetCompanyName);
        InventoryPostingGroup.ChangeCompany(TargetCompanyName);
        ProductionBOMHeader.ChangeCompany(TargetCompanyName);

        if not PackagingType.Get(SourceItem."Packaging Type SMK") then
            Message(NotFoundErr, SourceItem.FieldCaption("Packaging Type SMK"), SourceItem."Packaging Type SMK", TargetCompanyName);

        if not ItemCategory.Get(SourceItem."Item Category Code") then
            Message(NotFoundErr, SourceItem.FieldCaption("Item Category Code"), SourceItem."Item Category Code", TargetCompanyName);

        if not GenProductPostingGroup.Get(SourceItem."Gen. Prod. Posting Group") then
            Message(NotFoundErr, SourceItem.FieldCaption("Gen. Prod. Posting Group"), SourceItem."Gen. Prod. Posting Group", TargetCompanyName);

        if not VATProductPostingGroup.Get(SourceItem."VAT Prod. Posting Group") then
            Message(NotFoundErr, SourceItem.FieldCaption("VAT Prod. Posting Group"), SourceItem."VAT Prod. Posting Group", TargetCompanyName);

        if not InventoryPostingGroup.Get(SourceItem."Inventory Posting Group") then
            Message(NotFoundErr, SourceItem.FieldCaption("Inventory Posting Group"), SourceItem."Inventory Posting Group", TargetCompanyName);

        if not ProductionBOMHeader.Get(SourceItem."Production BOM No.") then
            Message(NotFoundErr, SourceItem.FieldCaption("Production BOM No."), SourceItem."Production BOM No.", TargetCompanyName);
    end;
    #endregion Copy Item

    #region Copy Item Variant
    procedure CopyItemVariantsToSelectedCompany(var SourceItemVariant: Record "Item Variant")
    var
        NewItem: Record Item;
        SourceItem: Record Item;
        ConfirmLbl: Label 'You need to copy Item No.: %1 first. Do you want to copy it now?', Comment = '%1=SourceItemVariant."Item No."';
        SuccesMsg: Label '%1 Item Variants copied to %2.', Comment = '%1="Item Variant".Count; %2=TargetCompanyName';
        FailedErr: Label 'Copy of %1 Item Variants to %2 failed.', Comment = '%1="Item Variant".Count; %2=TargetCompanyName';
        ConfirmTxt: Text;
    begin
        CompanySelection();

        NewItem.ChangeCompany(TargetCompanyName);

        SourceItemVariant.FindSet();

        ConfirmTxt := StrSubstNo(ConfirmLbl, SourceItemVariant."Item No.");
        if not NewItem.Get(SourceItemVariant."Item No.") then
            if ConfirmManagement.GetResponseOrDefault(ConfirmTxt, true) then begin
                SourceItem.Get(SourceItemVariant."Item No.");
                StartSession(SessionID, Codeunit::"Copy Item SMK", TargetCompanyName, SourceItem);
            end
            else
                Error(ProcessAbortedErr);
        repeat
            OK := StartSession(SessionID, Codeunit::"Copy Item Variant SMK", TargetCompanyName, SourceItemVariant);
        until SourceItemVariant.Next() = 0;

        if OK then
            Message(SuccesMsg, SourceItemVariant.Count(), TargetCompanyName)
        else
            Error(FailedErr, SourceItemVariant.Count(), TargetCompanyName);

    end;
    #endregion Copy Item Variant

    #region Copy BoM Header and Line
    procedure CopyBoMHeaderLineToSelectedCompany(ProductionBOMHeader: Record "Production BOM Header")
    var
        //NewProductionBOMHeader: Record "Production BOM Header";
        ProductionBOMLine: Record "Production BOM Line";
        NewProductionBOMLine: Record "Production BOM Line";
        TargetProductionBOMHeader: Record "Production BOM Header";
        TargetCompanyItem: Record Item;
        SuccesMsg: Label 'Production BOM Header and Line copied to %1.', Comment = '%1=TargetCompanyName';
    begin
        CompanySelection();

        TargetCompanyItem.ChangeCompany(TargetCompanyName);
        TargetProductionBOMHeader.ChangeCompany(TargetCompanyName);

        ProductionBOMLine.SetRange("Production BOM No.", ProductionBOMHeader."No.");
        ProductionBOMLine.SetRange("Version Code", ProductionBOMHeader."Version Nos.");
        ProductionBOMLine.FindSet(false);
        repeat
            case ProductionBOMLine.Type of
                "Production BOM Line Type"::Item:
                    if not TargetCompanyItem.Get(ProductionBOMLine."No.") then
                        Error(NotFoundErr, ProductionBOMLine.FieldCaption("No."), ProductionBOMLine."No.", TargetCompanyName);

                "Production BOM Line Type"::"Production BOM":
                    if not TargetProductionBOMHeader.Get(ProductionBOMLine."No.") then
                        Error(NotFoundErr, ProductionBOMLine.FieldCaption("No."), ProductionBOMLine."No.", TargetCompanyName);
            end;
        until ProductionBOMLine.Next() = 0;

        StartSession(SessionID, Codeunit::"Copy Production BOM Header SMK", TargetCompanyName, ProductionBOMHeader);

        ProductionBOMLine.FindSet(false);
        NewProductionBOMLine.ChangeCompany(TargetCompanyName);
        NewProductionBOMLine.SetRange("Production BOM No.", ProductionBOMLine."Production BOM No.");
        NewProductionBOMLine.SetRange("Version Code", ProductionBOMLine."Version Code");
        NewProductionBOMLine.DeleteAll(false);
        repeat
            NewProductionBOMLine.Init();
            NewProductionBOMLine.TransferFields(ProductionBOMLine);
            NewProductionBOMLine.Insert(false);
        until ProductionBOMLine.Next() = 0;

        Message(SuccesMsg, TargetCompanyName);
    end;
    #endregion Copy BoM Header and Line

    #region Copy Quality Control Specs

    procedure CopyQualityControlSpecs(var QualityControlSpec: Record "Quality Control Spec. SMK")
    var
        QualityControlCopiedMsg: Label '%1 Quality Control Specs copied to %2.', Comment = '%1="Quality Control Spec. SMK".Count; %2=TargetCompanyName';
    begin
        CompanySelection();

        if not QualityControlSpec.FindSet(false) then
            exit;

        repeat
            StartSession(SessionID, Codeunit::"Copy Quality Control Spec. SMK", TargetCompanyName, QualityControlSpec);
        until QualityControlSpec.Next() = 0;

        Message(QualityControlCopiedMsg, QualityControlSpec.Count(), TargetCompanyName);
    end;
    #endregion Copy Quality Control Specs

    #region Copy Item - Quality Control Specs

    procedure CopyItemQualityControlSpecs(var ItemQualityControlSpec: Record "Item Quality Control Spec. SMK")
    var
        QualityItemControlCopiedMsg: Label '%1 Item - Quality Control Specs copied to %2.', Comment = '%1="Item Quality Control Spec. SMK".Count; %2=TargetCompanyName';
    begin
        CompanySelection();

        if not ItemQualityControlSpec.FindSet(false) then
            exit;

        repeat
            StartSession(SessionID, Codeunit::"Copy Item Qlty. Ctrl. Spec SMK", TargetCompanyName, ItemQualityControlSpec);
        until ItemQualityControlSpec.Next() = 0;

        Message(QualityItemControlCopiedMsg, ItemQualityControlSpec.Count(), TargetCompanyName);
    end;

    #endregion Copy Item - Quality Control Specs

    local procedure CompanySelection()
    var
        Company: Record Company;
        SameCompanyErr: Label 'You cannot copy an item to the same company.';
    begin
        if Page.RunModal(Page::Companies, Company) = Action::LookupOK then
            TargetCompanyName := Company.Name;

        if TargetCompanyName = CompanyName() then
            Error(SameCompanyErr);
    end;

    var
        ConfirmManagement: Codeunit "Confirm Management";
        NotFoundErr: Label '%1 %2 not found in Company %3', Comment = '%1=FieldCaption("Production BOM No."); %2=Item."Production BOM No."; %3=TargetCompanyNameLbl';
        ProcessAbortedErr: Label 'Process aborted by user.';
        TargetCompanyName: Text;
        OK: Boolean;
        SessionID: Integer;
}