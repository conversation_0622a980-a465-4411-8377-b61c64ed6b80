table 60005 "Production Parameter SMK"
{
    Caption = 'Production Parameter';
    DataClassification = CustomerContent;
    DrillDownPageId = "Production Parameters SMK";
    LookupPageId = "Production Parameters SMK";

    fields
    {
        field(1; "Machine No."; Code[20])
        {
            Caption = 'Work Center/Machine No.';
            TableRelation = if (Type = const("Machine Center")) "Machine Center"
            else if (Type = const("Work Center")) "Work Center";
            ToolTip = 'Specifies the value of the Machine No. field.';
        }
        field(2; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            TableRelation = Item."No.";
            ToolTip = 'Specifies the value of the Item No. field.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if not Item.Get("Item No.") then
                    "Item Description" := ''
                else
                    "Item Description" := Item.Description;
            end;
        }
        field(4; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }

        field(5; "Barrel Temperature (°C)"; Integer)
        {
            Caption = 'Barrel Temperature (°C)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Temperature Parameter SMK" where("Machine No." = field("Machine No."), "Item No." = field("Item No.")));
            ToolTip = 'Specifies the value of the Barrel Temperature (°C) field.';
        }

        field(6; "Screw Speed (rpm)"; Text[50])
        {
            Caption = 'Screw Speed (rpm)';
            ToolTip = 'Specifies the value of the Screw Speed (rpm) field.';
        }
        field(7; "Torque (%)"; Text[50])
        {
            Caption = 'Torque (%)';
            ToolTip = 'Specifies the value of the Torque (%) field.';
        }
        field(8; "Vaccuum (bar)"; Text[50])
        {
            Caption = 'Vaccuum (bar)';
            ToolTip = 'Specifies the value of the Vaccuum (bar) field.';
        }
        field(9; "Output (kg/saat)"; Text[50])
        {
            Caption = 'Output (kg/saat)';
            ToolTip = 'Specifies the value of the Output (kg/saat) field.';
        }
        field(10; "Bath Temperature"; Text[50])
        {
            Caption = 'Bath Temperature';
            ToolTip = 'Specifies the value of the Bath Temperature field.';
        }
        field(11; "Filter Diameter (Mesh)"; Text[50])
        {
            Caption = 'Filter Diameter (Mesh)';
            ToolTip = 'Specifies the value of the Filter Diameter (Mesh).';
        }
        field(3; Type; Enum "Capacity Type Journal")
        {
            Caption = 'Type';
            ValuesAllowed = 0, 1;
            ToolTip = 'Specifies the value of the Type field.';
        }
        field(25; "Pelletization Speed (m/min)"; Decimal)
        {
            Caption = 'Pelletization Speed (m/min)';
            ToolTip = 'Specifies the value of the Pelletization Speed (m/min) field.';
            DecimalPlaces = 0 : 2;
        }
        field(26; "Pellet Temperature (°C)"; Decimal)
        {
            Caption = 'Pellet Temperature (°C)';
            ToolTip = 'Specifies the value of the Pellet Temperature (°C) field.';
            DecimalPlaces = 0 : 2;
        }
    }
    keys
    {
        key(PK; "Machine No.", "Item No.")
        {
            Clustered = true;
        }
    }
}