page 60040 "Enter Test Result for Pkg SMK"
{
    ApplicationArea = All;
    Caption = 'Enter Test Result for Package';
    PageType = StandardDialog;
    UsageCategory = Tasks;

    layout
    {
        area(Content)
        {
            field("Package No."; PackageNo)
            {
                Caption = 'Package No.';
                ToolTip = 'Specifies the value of the Package No. field.';
                trigger OnLookup(var Text: Text): Boolean
                begin
                    PackageNo := SumikaProductionMgt.OnAfterLookupPackageNo_ReadBarcodePage();
                end;
            }
        }
    }
    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction = CloseAction::Ok then
            SumikaQualityCtrlMgt.OpenQualityControlDocumentAndPopulateQCLineDetailsFromPackageNo(PackageNo);
    end;

    var
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
        PackageNo: Code[50];
}