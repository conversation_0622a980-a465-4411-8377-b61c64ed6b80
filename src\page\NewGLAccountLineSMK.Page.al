page 60021 "New G/L Account Line SMK"
{
    ApplicationArea = All;
    Caption = 'New G/L Account Line';
    PageType = StandardDialog;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("Shortcut Dimension 1 Code"; Dim1)
                {
                    Caption = 'Shortcut Dimension 1 Code';
                    ToolTip = 'Specifies the code for Shortcut Dimension 1, which is one of two global dimension codes that you set up in the General Ledger Setup window.';
                    TableRelation = "Dimension Value".Code where("Global Dimension No." = const(1));
                    trigger OnValidate()
                    begin
                        GlAccountNo := DimensionGLAccMgt.GetGLAccountNoFromDimensions(Dim1, Dim2)
                    end;
                }
                field("Shortcut Dimension 2 Code"; Dim2)
                {
                    Caption = 'Shortcut Dimension 2 Code';
                    ToolTip = 'Specifies the code for Shortcut Dimension 2, which is one of two global dimension codes that you set up in the General Ledger Setup window.';
                    TableRelation = "Dimension Value".Code where("Global Dimension No." = const(2));
                    trigger OnValidate()
                    begin
                        GlAccountNo := DimensionGLAccMgt.GetGLAccountNoFromDimensions(Dim1, Dim2)
                    end;
                }
                field("No."; GlAccountNo)
                {
                    Caption = 'G/L Account No.';
                    ToolTip = 'Specifies the number of the involved entry or record, according to the specified number series.';
                    Editable = false;
                }
            }
        }
    }
    trigger OnClosePage()
    begin
        if GlAccountNo <> '' then
            if PurchaseHeader."No." <> '' then
                DimensionGLAccMgt.InsertGLAccountLineToPurchaseLine(PurchaseHeader, GlAccountNo, Dim1, Dim2)
            else
                if GenJournalTemplateName <> '' then
                    DimensionGLAccMgt.InsertGLAccountLineToGeneralJournalLine(GenJournalTemplateName, GenJournalBatchName, GlAccountNo, Dim1, Dim2);
    end;

    procedure GetPurchaseHeader(ParamPurchaseHeader: Record "Purchase Header")
    begin
        PurchaseHeader := ParamPurchaseHeader;
    end;

    procedure GetTemplateAndBatchName(ParamTemplateName: Code[10]; ParamBatchName: Code[10])
    begin
        GenJournalTemplateName := ParamTemplateName;
        GenJournalBatchName := ParamBatchName;
        //Message('Template Name: %1, Batch Name: %2', GenJournalTemplateName, GenJournalBatchName);
    end;

    var
        PurchaseHeader: Record "Purchase Header";
        DimensionGLAccMgt: Codeunit "Dimension - G/L Acc. Mgt. SMK";
        GenJournalTemplateName: Code[10];
        GenJournalBatchName: Code[10];
        Dim1: Code[20];
        Dim2: Code[20];
        GlAccountNo: Code[20];
}