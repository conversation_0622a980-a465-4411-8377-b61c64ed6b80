page 60031 "Packaging Type List SMK"
{
    ApplicationArea = All;
    Caption = 'Packaging Type List';
    PageType = List;
    SourceTable = "Packaging Type SMK";
    UsageCategory = Administration;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Code"; Rec.Code)
                {
                }
                field(Description; Rec.Description)
                {
                }
                field("Allow Package Split"; Rec."Allow Package Split")
                {
                }
            }
        }
    }
}