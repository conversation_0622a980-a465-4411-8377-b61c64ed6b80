page 60034 "Package Split Subpage SMK"
{
    ApplicationArea = All;
    Caption = 'Package Split Subpage';
    PageType = ListPart;
    SourceTable = "Package Split Line SMK";
    InsertAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("New Package No."; Rec."New Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                    // trigger OnValidate()
                    // begin
                    //     CurrPage.Update();
                    // end;
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Source Package No."; Rec."Source Package No.")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action("PackageLabel SMK")
            {
                ApplicationArea = All;
                Caption = 'Print Selected Package Labels';
                // Promoted = true;
                // PromotedCategory = Process;
                // PromotedIsBig = true;
                Image = BarCode;
                ToolTip = 'Executes the Print Package Label action.';

                trigger OnAction()
                var
                    PackageNoInformation: Record "Package No. Information";
                    PackageSplitLine: Record "Package Split Line SMK";
                    PackageSplitHeader: Record "Package Split Header SMK";
                begin
                    PackageSplitHeader.Get(Rec."Document No.");
                    PackageSplitHeader.TestField(Completed);

                    CurrPage.SetSelectionFilter(PackageSplitLine);
                    PackageSplitLine.FindSet(false);
                    repeat
                        PackageNoInformation.Get(PackageSplitLine."Item No.", PackageSplitLine."Variant Code", PackageSplitLine."New Package No.");
                        PackageNoInformation.Mark(true);
                    until PackageSplitLine.Next() = 0;

                    PackageNoInformation.MarkedOnly(true);
                    Report.Run(Report::"Package Label SMK", true, true, PackageNoInformation);
                end;
            }
        }
    }
}