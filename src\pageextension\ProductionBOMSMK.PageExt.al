pageextension 60037 "Production BOM SMK" extends "Production BOM"
{
    layout
    {
        addlast(General)
        {
            field("Total Weight (KG) SMK"; Rec."Total Weight (KG) SMK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addlast("&Prod. BOM")
        {
            action("CopyProductionBOMToAnotherCompany SMK")
            {
                Caption = 'Copy Prod. BOM to Another Company';
                Image = CopyBOM;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ApplicationArea = All;
                ToolTip = 'Executes the Copy Prod. BOM to Another Company action.';
                trigger OnAction()
                begin
                    DataSyncManagement.CopyBoMHeaderLineToSelectedCompany(Rec);
                end;
            }
        }
    }
    var
        DataSyncManagement: Codeunit "Data Sync. Management SMK";
}