page 60009 "Copy Prod. Parameter Dialog"
{
    ApplicationArea = All;
    Caption = 'Copy Prod. Parameter Dialog';
    PageType = ConfirmationDialog;
    SourceTable = "Production Parameter SMK";

    layout
    {
        area(Content)
        {
            field(NewMachineCenter; NewMachineCenter)
            {
                Caption = 'New Machine Center No.';
                ToolTip = 'Specifies the value of the New Machine Center No. field.';
                TableRelation = "Machine Center"."No.";
            }
            field(NewItemNo; NewItemNo)
            {
                Caption = 'New Item No.';
                ToolTip = 'Specifies the value of the New Item No. field.';
                TableRelation = Item."No.";
            }
        }
    }

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    begin
        if CloseAction = CloseAction::Yes then
            SumikaProductionMgt.CopyProductionParameterRecord(Rec, NewMachineCenter, NewItemNo);
    end;

    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
        NewMachineCenter: Code[10];
        NewItemNo: Code[20];
}