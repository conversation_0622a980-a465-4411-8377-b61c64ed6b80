page 60029 "Item Variants SMK"
{
    ApplicationArea = All;
    Caption = 'Item Variants';
    PageType = List;
    SourceTable = "Item Variant";
    UsageCategory = Lists;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the item card from which you opened the Item Variant Translations window.';
                }
                field("Item Description SMK"; Rec."Item Description SMK")
                {
                }
                field(Code; Rec.Code)
                {
                    ToolTip = 'Specifies a code to identify the variant.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies text that describes the item variant.';
                }
            }
        }
    }
}