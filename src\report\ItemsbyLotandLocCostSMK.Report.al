report 60006 "Items by Lot and Loc.-Cost SMK"
{
    ApplicationArea = All;
    Caption = 'Items by Lot and Location - Cost';
    UsageCategory = ReportsAndAnalysis;
    DataAccessIntent = ReadOnly;

    dataset
    {
        dataitem(ItemLedgerEntry; "Item Ledger Entry")
        {
            RequestFilterFields = "Date Filter SMK", "Posting Date";

            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(Description; Description)
            {
            }
            column(LocationCode; "Location Code")
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(Quantity; Quantity)
            {
            }
            column(UnitofMeasureCode; "Unit of Measure Code")
            {
            }
            column(ItemDescription; SumikaPurchaseManagement.GetItemDescriptionFromNoAndVariantCode(ItemLedgerEntry."Item No.", ''))
            {
            }
            column(CostAmtActualbyDateSMK_ItemLedgerEntry; "Cost Amt. (Actual) by Date SMK")
            {
            }
            column(PostingDate_ItemLedgerEntry; "Posting Date")
            {
            }
            column(CompanyName; CompanyNameText)
            {
            }
            column(LCYCode; LCYCodeText)
            {
            }
            column(ReportFilters; ReportFiltersText)
            {
            }
            column(dateFilter; "Date Filter SMK")
            {
            }
            column(EntryDate; GetFirstEntryDateFromItemLedgerEntry(ItemLedgerEntry))
            {
            }

            trigger OnPreDataItem()
            begin
                // Set load fields for performance optimization - only essential fields for layout
                SetLoadFields("Item No.", "Variant Code", Description, "Location Code", "Lot No.",
                             Quantity, "Unit of Measure Code", "Posting Date", "Date Filter SMK");

                // Initialize values that are the same for all records (performance optimization)
                CompanyNameText := CompanyName();
                LCYCodeText := SumikaBasicFunctions.GetLCYCode();
                ReportFiltersText := GetFilters();

                // Initialize cache for first entry date calculations
                TempFirstEntryDateCache.DeleteAll(false);
                FirstEntryDateCacheCounter := 1;
            end;
        }
    }

    local procedure GetFirstEntryDateFromItemLedgerEntry(ItemLedgerEntry: Record "Item Ledger Entry"): Date
    var
        FirstEntryItemLedgerEntry: Record "Item Ledger Entry";
        CachedDate: Date;
    begin
        // Use Item No. + Variant Code + Lot No. as unique identifier for caching
        TempFirstEntryDateCache.SetRange("Item No.", ItemLedgerEntry."Item No.");
        TempFirstEntryDateCache.SetRange("Variant Code", ItemLedgerEntry."Variant Code");
        TempFirstEntryDateCache.SetRange("Lot No.", ItemLedgerEntry."Lot No.");

        // Check if we already calculated this combination
        if TempFirstEntryDateCache.FindFirst() then begin
            TempFirstEntryDateCache.Reset();
            exit(TempFirstEntryDateCache."Posting Date"); // We store the first entry date in Posting Date field
        end;

        TempFirstEntryDateCache.Reset();

        // Calculate first entry date for new combination
        FirstEntryItemLedgerEntry.SetLoadFields("Posting Date");
        FirstEntryItemLedgerEntry.SetCurrentKey("Item No.", "Variant Code", "Lot No.", "Posting Date");

        FirstEntryItemLedgerEntry.SetRange(Positive, true);
        FirstEntryItemLedgerEntry.SetRange("Item No.", ItemLedgerEntry."Item No.");
        FirstEntryItemLedgerEntry.SetRange("Variant Code", ItemLedgerEntry."Variant Code");
        FirstEntryItemLedgerEntry.SetRange("Lot No.", ItemLedgerEntry."Lot No.");

        if FirstEntryItemLedgerEntry.FindFirst() then
            CachedDate := FirstEntryItemLedgerEntry."Posting Date"
        else
            CachedDate := 0D;

        // Store in cache for future use
        TempFirstEntryDateCache.Init();
        TempFirstEntryDateCache."Entry No." := FirstEntryDateCacheCounter; // Use counter as unique key
        TempFirstEntryDateCache."Item No." := ItemLedgerEntry."Item No.";
        TempFirstEntryDateCache."Variant Code" := ItemLedgerEntry."Variant Code";
        TempFirstEntryDateCache."Lot No." := ItemLedgerEntry."Lot No.";
        TempFirstEntryDateCache."Posting Date" := CachedDate; // Store the calculated first entry date
        TempFirstEntryDateCache.Insert(false);

        FirstEntryDateCacheCounter += 1;

        exit(CachedDate);
    end;

    trigger OnInitReport()
    begin
        StartTime := CurrentDateTime();
    end;

    trigger OnPostReport()
    var
        Runtime: Duration;
        RuntimeText: Text;
    begin
        Runtime := CurrentDateTime() - StartTime;
        RuntimeText := Format(Runtime);
        Message('Report completed successfully in %1.', RuntimeText);
    end;

    var
        TempFirstEntryDateCache: Record "Item Ledger Entry" temporary;
        SumikaPurchaseManagement: Codeunit "Sumika Purchase Management SMK";
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
        CompanyNameText: Text;
        LCYCodeText: Code[10];
        ReportFiltersText: Text;
        FirstEntryDateCacheCounter: Integer;
        StartTime: DateTime;
}