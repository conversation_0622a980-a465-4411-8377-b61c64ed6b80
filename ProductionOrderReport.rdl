﻿<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2016/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DataSource">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString />
      </ConnectionProperties>
      <rd:SecurityType>None</rd:SecurityType>
    </DataSource>
  </DataSources>
  <ReportSections>
    <ReportSection>
      <Body>
        <Height>2in</Height>
        <Style />
      </Body>
      <Width>6.5in</Width>
      <Page>
        <Style />
      </Page>
    </ReportSection>
  </ReportSections>
  <Code>Public Function BlankZero(ByVal Value As Decimal)
    if Value = 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankPos(ByVal Value As Decimal)
    if Value &gt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankZeroAndPos(ByVal Value As Decimal)
    if Value &gt;= 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNeg(ByVal Value As Decimal)
    if Value &lt; 0 then
        Return ""
    end if
    Return Value
End Function

Public Function BlankNegAndZero(ByVal Value As Decimal)
    if Value &lt;= 0 then
        Return ""
    end if
    Return Value
End Function
</Code>
  <Language>=User!Language</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Inch</rd:ReportUnitType>
  <rd:ReportID>0eeb6585-38ae-40f1-885b-8d50088d51b4</rd:ReportID>
  <DataSets>
    <DataSet Name="DataSet_Result">
      <Fields>
        <Field Name="No">
          <DataField>No</DataField>
        </Field>
        <Field Name="MachineScaleNoSMK_ProductionOrder">
          <DataField>MachineScaleNoSMK_ProductionOrder</DataField>
        </Field>
        <Field Name="PackagingType_ProductionOrder">
          <DataField>PackagingType_ProductionOrder</DataField>
        </Field>
        <Field Name="Description">
          <DataField>Description</DataField>
        </Field>
        <Field Name="SourceNo">
          <DataField>SourceNo</DataField>
        </Field>
        <Field Name="VariantCode">
          <DataField>VariantCode</DataField>
        </Field>
        <Field Name="StartingDate">
          <DataField>StartingDate</DataField>
        </Field>
        <Field Name="EndingDate">
          <DataField>EndingDate</DataField>
        </Field>
        <Field Name="Quantity">
          <DataField>Quantity</DataField>
        </Field>
        <Field Name="QuantityFormat">
          <DataField>QuantityFormat</DataField>
        </Field>
        <Field Name="BarrelTempCSMK">
          <DataField>BarrelTempCSMK</DataField>
        </Field>
        <Field Name="ScrewSpeedrpmSMK">
          <DataField>ScrewSpeedrpmSMK</DataField>
        </Field>
        <Field Name="TorqueSMK">
          <DataField>TorqueSMK</DataField>
        </Field>
        <Field Name="PelletizationSpeedm_min_SMK">
          <DataField>PelletizationSpeedm_min_SMK</DataField>
        </Field>
        <Field Name="PelletizationSpeedm_min_SMKFormat">
          <DataField>PelletizationSpeedm_min_SMKFormat</DataField>
        </Field>
        <Field Name="PelletTemperature__C_SMK">
          <DataField>PelletTemperature__C_SMK</DataField>
        </Field>
        <Field Name="PelletTemperature__C_SMKFormat">
          <DataField>PelletTemperature__C_SMKFormat</DataField>
        </Field>
        <Field Name="VaccuumbarSMK">
          <DataField>VaccuumbarSMK</DataField>
        </Field>
        <Field Name="OutputkgsaatSMK">
          <DataField>OutputkgsaatSMK</DataField>
        </Field>
        <Field Name="BathTemperatureCSMK">
          <DataField>BathTemperatureCSMK</DataField>
        </Field>
        <Field Name="FilterDiameterMeshSMK">
          <DataField>FilterDiameterMeshSMK</DataField>
        </Field>
        <Field Name="MachineNoSMK">
          <DataField>MachineNoSMK</DataField>
        </Field>
        <Field Name="EncodedText">
          <DataField>EncodedText</DataField>
        </Field>
        <Field Name="LineNo_ProdOrderComponent">
          <DataField>LineNo_ProdOrderComponent</DataField>
        </Field>
        <Field Name="ItemNo_ProdOrderComponent">
          <DataField>ItemNo_ProdOrderComponent</DataField>
        </Field>
        <Field Name="VariantCode_ProdOrderComponent">
          <DataField>VariantCode_ProdOrderComponent</DataField>
        </Field>
        <Field Name="Description_ProdOrderComponent">
          <DataField>Description_ProdOrderComponent</DataField>
        </Field>
        <Field Name="Quantityper_ProdOrderComponent">
          <DataField>Quantityper_ProdOrderComponent</DataField>
        </Field>
        <Field Name="Quantityper_ProdOrderComponentFormat">
          <DataField>Quantityper_ProdOrderComponentFormat</DataField>
        </Field>
        <Field Name="QtyperUnitofMeasure_ProdOrderComponent">
          <DataField>QtyperUnitofMeasure_ProdOrderComponent</DataField>
        </Field>
        <Field Name="QtyperUnitofMeasure_ProdOrderComponentFormat">
          <DataField>QtyperUnitofMeasure_ProdOrderComponentFormat</DataField>
        </Field>
        <Field Name="ExpectedQuantity_ProdOrderComponent">
          <DataField>ExpectedQuantity_ProdOrderComponent</DataField>
        </Field>
        <Field Name="ExpectedQuantity_ProdOrderComponentFormat">
          <DataField>ExpectedQuantity_ProdOrderComponentFormat</DataField>
        </Field>
        <Field Name="FeederNoSMK_ProdOrderComponent">
          <DataField>FeederNoSMK_ProdOrderComponent</DataField>
        </Field>
        <Field Name="LotNo_ReservationEntry">
          <DataField>LotNo_ReservationEntry</DataField>
        </Field>
        <Field Name="BarrelNo_TemperatureParameterSMK">
          <DataField>BarrelNo_TemperatureParameterSMK</DataField>
        </Field>
        <Field Name="TemperatureC_TemperatureParameterSMK">
          <DataField>TemperatureC_TemperatureParameterSMK</DataField>
        </Field>
        <Field Name="TemperatureC_TemperatureParameterSMKFormat">
          <DataField>TemperatureC_TemperatureParameterSMKFormat</DataField>
        </Field>
        <Field Name="LotNoSMK_ProdOrderLine">
          <DataField>LotNoSMK_ProdOrderLine</DataField>
        </Field>
        <Field Name="Description2">
          <DataField>Description2</DataField>
        </Field>
        <Field Name="Name_CompanyInformation">
          <DataField>Name_CompanyInformation</DataField>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DataSource</DataSourceName>
        <CommandText />
      </Query>
    </DataSet>
  </DataSets>
</Report>