tableextension 60007 "Package No. Information SMK" extends "Package No. Information"
{
    fields
    {
        field(60000; "Lot No. SMK"; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(60001; "Expiration Date SMK"; Date)
        {
            Caption = 'Expiration Date';
            ToolTip = 'Specifies the value of the Expiration Date field.';
            AllowInCustomizations = Always;
        }
        field(60002; "Inventory Entry Date SMK"; Date)
        {
            Caption = 'Inventory Entry Date';
            ToolTip = 'Specifies the value of the Inventory Entry Date field.';
        }
        field(60003; "Shelf Life SMK"; DateFormula)
        {
            Caption = 'Shelf Life';
            ToolTip = 'Specifies the value of the Shelf Life field.';
        }
        field(60004; "Production Order No. SMK"; Code[20])
        {
            Caption = 'Production Order No.';
            TableRelation = "Production Order"."No." where(Status = const(Released));
            ToolTip = 'Specifies the value of the Production Order No. field.';
        }
        field(60005; "Parent Package No. SMK"; Code[50])
        {
            Caption = 'Parent Package No.';
            TableRelation = "Package No. Information"."Package No." where("Parent Package No. SMK" = filter(''));
            ToolTip = 'Specifies the value of the Parent Package No. field.';
        }
        field(60006; "Location Code SMK"; Code[10])
        {
            Caption = 'Location Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Ledger Entry"."Location Code" where("Item No." = field("Item No."),
                                                                  "Variant Code" = field("Variant Code"),
                                                                  "Package No." = field("Package No."),
                                                                  Open = const(true)));
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(60007; "Parent Package Inventory SMK"; Decimal)
        {
            Caption = 'Parent Package Inventory';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Item No."),
                                                                  "Variant Code" = field("Variant Code"),
                                                                  "Location Code" = field("Location Filter"),
                                                                  "Parent Package No. SMK" = field("Package No.")));
            AllowInCustomizations = Always;
        }
        field(60008; "Vendor Lot No. SMK"; Code[50])
        {
            Caption = 'Vendor Lot No.';
            // Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = lookup("Lot No. Information"."Vendor Lot No. SMK" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Lot No." = field("Lot No. SMK")));
            ToolTip = 'Specifies the value of the Vendor Lot No. field.';
        }
        field(60009; "Inventory Exist SMK"; Boolean)
        {
            Caption = 'Inventory Exist';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Item Ledger Entry" where("Item No." = field("Item No."),
                                                                  "Variant Code" = field("Variant Code"),
                                                                  "Package No." = field("Package No."),
                                                                  Open = const(true)));
            AllowInCustomizations = Always;
        }
        field(60010; "Old Package No. SMK"; Code[50])
        {
            Caption = 'Old Package No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Old Package No. field.';
        }
        field(60011; "Package Order No. SMK"; Integer)
        {
            Caption = 'Package Order No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Package Order No. field.';
        }
        field(60012; "Description 2 SMK"; Text[50])
        {
            Caption = 'Description 2';
            ToolTip = 'Specifies the value of the Description 2 field.';
        }
        field(60013; "Packaging Type Code SMK"; Code[100])
        {
            Caption = 'Packaging Type Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("Item No.")));
            ToolTip = 'Specifies the value of the Packaging Type Code field.';
        }
        field(60014; "Sales Blocked SMK"; Boolean)
        {
            Caption = 'Sales Blocked';
            ToolTip = 'Specifies the value of the Label Quantity field.';
            AllowInCustomizations = Always;
        }
        field(60015; "Production Blocked SMK"; Boolean)
        {
            Caption = 'Production Blocked';
            ToolTip = 'Specifies the value of the Label Quantity field.';
            AllowInCustomizations = Always;
        }
        field(60016; "Label Quantity SMK"; Decimal)
        {
            Caption = 'Label Quantity';
            ToolTip = 'Specifies the value of the Label Quantity field.';
            AllowInCustomizations = Always;
        }
        field(60017; "Quality Control Status SMK"; Enum "Quality Control Status SMK")
        {
            Caption = 'Quality Control Status';
            InitValue = Acceptance;
            ToolTip = 'Specifies the value of the Quality Control Status field.';
        }
        field(60018; "Comment Count SMK"; Integer)
        {
            Caption = 'Comment Count';
            ToolTip = 'Specifies the value of the Comment Count field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Item Tracking Comment" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Serial/Lot No." = field("Package No.")));
        }
    }
    keys
    {
        key(SMK; "Parent Package No. SMK")
        {
        }
    }
    fieldgroups
    {
        addlast(DropDown; "Package Order No. SMK")
        {
        }
    }
    trigger OnAfterInsert()
    var
        Item: Record Item;
    begin
        if Item.Get(Rec."Item No.") then
            Rec."Description 2 SMK" := Item."Description 2"
    end;
}