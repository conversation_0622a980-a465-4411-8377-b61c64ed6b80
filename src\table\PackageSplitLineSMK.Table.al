table 60022 "Package Split Line SMK"
{
    Caption = 'Package Split Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            Editable = false;
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            Editable = false;
            AllowInCustomizations = Always;
        }
        field(3; "New Package No."; Code[50])
        {
            Caption = 'New Package No.';
            Editable = false;
            ToolTip = 'Specifies the value of the New Package No. field.';
        }
        field(4; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(5; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            Editable = false;
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(6; Quantity; Decimal)
        {
            Caption = 'Quantity';
            Editable = false;
            DecimalPlaces = 0 : 4;
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(7; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            Editable = false;
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(8; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location.Code;
            Editable = false;
            ToolTip = 'Specifies the value of the Location Code field.';
        }
        field(9; "Source Package No."; Code[50])
        {
            Caption = 'Source Package No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package Split Header SMK"."Package No." where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Source Package No. field.';
        }
        field(10; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        PackageSplitLine: Record "Package Split Line SMK";
    begin
        PackageSplitLine.SetRange("Document No.", Rec."Document No.");
        if PackageSplitLine.FindLast() then
            Rec."Line No." := PackageSplitLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}