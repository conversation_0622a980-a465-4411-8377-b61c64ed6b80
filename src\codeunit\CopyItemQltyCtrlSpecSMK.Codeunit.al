codeunit 60013 "Copy Item Qlty. Ctrl. Spec SMK"
{
    TableNo = "Item Quality Control Spec. SMK";

    trigger OnRun()
    begin
        if NewItemQualityControlSpec.Get(Rec."Item No.", Rec.Type, Rec."Specification Code") then begin
            NewItemQualityControlSpec.TransferFields(Rec);
            NewItemQualityControlSpec.Modify(true);
        end
        else begin
            NewItemQualityControlSpec.Init();
            NewItemQualityControlSpec.TransferFields(Rec);
            NewItemQualityControlSpec.Insert(true);
        end;
    end;

    var
        NewItemQualityControlSpec: Record "Item Quality Control Spec. SMK";
}