page 60041 "Package Quality Controls SMK"
{
    ApplicationArea = All;
    Caption = 'Package Quality Controls';
    PageType = List;
    SourceTable = "Quality Control Header SMK";
    UsageCategory = Lists;
    CardPageId = "Package Quality Control Doc.";
    Editable = false;
    SourceTableView = where("Package No." = filter(<> ''));
    InsertAllowed = false;
    DeleteAllowed = false;
    ModifyAllowed = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {

                field("No."; Rec."No.")
                {
                }
                field("Package No."; Rec."Package No.")
                {
                }
                field("Item No."; Rec."Item No.")
                {
                }
                field("Variant Code"; Rec."Variant Code")
                {
                }
                field("Item Description"; Rec."Item Description")
                {
                }
                field("Lot No."; Rec."Lot No.")
                {
                }
                field("Production Order No."; Rec."Production Order No.")
                {
                }
                field("QC Control Quantity"; Rec."QC Control Quantity")
                {
                }
                field("Package Order No."; Rec."Package Order No.")
                {
                }
                field(Posted; Rec.Posted)
                {
                }
            }
        }
    }
}