tableextension 60027 "Sales Invoice Line SMK" extends "Sales Invoice Line"
{
    fields
    {
        field(60001; "Sell-to Customer Name SMK"; Text[100])
        {
            Caption = 'Sell-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Invoice Header"."Sell-to Customer Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Sell-to Customer Name field.';
        }
        field(60002; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("No.")));
            ToolTip = 'Specifies the packaging type of the item.';
        }
    }
}