codeunit 60002 "Sumika Quality Ctrl. Mgt. SMK"
{
    SingleInstance = true;
    procedure PopualteItemQCSpecFromSpecCode(var ItemQualityControlSpec: Record "Item Quality Control Spec. SMK")
    var
        QualityControlSpec: Record "Quality Control Spec. SMK";
    begin
        if QualityControlSpec.Get(ItemQualityControlSpec."Specification Code") then begin
            ItemQualityControlSpec.Validate("Specification Description", QualityControlSpec.Description);
            ItemQualityControlSpec.Validate("Unit of Measure Code", QualityControlSpec."Unit of Measure Code");
            ItemQualityControlSpec.Validate(Standard, QualityControlSpec.Standard);
        end
        else begin
            ItemQualityControlSpec.Validate("Specification Description", '');
            ItemQualityControlSpec.Validate("Unit of Measure Code", '');
            ItemQualityControlSpec.Validate(Standard, '');
        end;
    end;

    procedure PopulateQualityControlLines(QualityControlHeader: Record "Quality Control Header SMK")
    var
        QualityControlLine: Record "Quality Control Line SMK";
        ItemQualityControlSpec: Record "Item Quality Control Spec. SMK";
    begin
        QualityControlHeader.TestField("Item No.");
        QualityControlHeader.TestField(Type);

        ItemQualityControlSpec.SetRange("Item No.", QualityControlHeader."Item No.");
        ItemQualityControlSpec.SetRange(Type, QualityControlHeader.Type);
        if not ItemQualityControlSpec.FindSet(false) then
            exit;

        repeat
            QualityControlLine.SetRange("Document No.", QualityControlHeader."No.");
            QualityControlLine.SetRange("Specification Code", ItemQualityControlSpec."Specification Code");
            if QualityControlLine.IsEmpty() then begin
                QualityControlLine.Init();
                QualityControlLine."Document No." := QualityControlHeader."No.";
                QualityControlLine.Insert(true);
                QualityControlLine.Validate("Specification Code", ItemQualityControlSpec."Specification Code");
                QualityControlLine.Validate("Specification Description", ItemQualityControlSpec."Specification Description");
                QualityControlLine.Validate("Specification Reference", ItemQualityControlSpec."Specification Reference");
                QualityControlLine.Validate("Min Value", ItemQualityControlSpec."Min Value");
                QualityControlLine.Validate("Max Value", ItemQualityControlSpec."Max Value");
                QualityControlLine.Validate("Exact Value", ItemQualityControlSpec."Exact Value");
                QualityControlLine.Validate("Selection Value", ItemQualityControlSpec."Selection Value");
                QualityControlLine.Validate("Unit of Measure Code", ItemQualityControlSpec."Unit of Measure Code");
                QualityControlLine.Validate(Standard, ItemQualityControlSpec.Standard);
                QualityControlLine.Modify(true);
            end;
        until ItemQualityControlSpec.Next() = 0;
    end;

    procedure CreateQualityControlLineDetailFromQualityControlLine(QualityControlLine: Record "Quality Control Line SMK")
    var
        QualityControlHeader: Record "Quality Control Header SMK";
        QualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
        EnterTestResultDialog: Page "Enter Test Result Dialog SMK";
    begin
        QualityControlHeader.Get(QualityControlLine."Document No.");

        QualityControlLineDtl.Init();
        QualityControlLineDtl."Document No." := QualityControlLine."Document No.";
        QualityControlLineDtl."Document Line No." := QualityControlLine."Line No.";
        QualityControlLineDtl.Insert(true);
        QualityControlLineDtl.Validate("Specification Code", QualityControlLine."Specification Code");
        QualityControlLineDtl.Validate("Specification Description", QualityControlLine."Specification Description");
        QualityControlLineDtl.Validate("Specification Reference", QualityControlLine."Specification Reference");
        QualityControlLineDtl.Validate("Min Value", QualityControlLine."Min Value");
        QualityControlLineDtl.Validate("Max Value", QualityControlLine."Max Value");
        QualityControlLineDtl.Validate("Exact Value", QualityControlLine."Exact Value");
        QualityControlLineDtl.Validate("Selection Value", QualityControlLine."Selection Value");
        QualityControlLineDtl.Validate("Unit of Measure Code", QualityControlLine."Unit of Measure Code");
        QualityControlLineDtl.Validate(Standard, QualityControlLine.Standard);
        QualityControlLineDtl.Validate("Item No.", QualityControlHeader."Item No.");
        QualityControlLineDtl.Validate("Variant Code", QualityControlHeader."Variant Code");
        QualityControlLineDtl.Validate("Lot No.", QualityControlHeader."Lot No.");
        QualityControlLineDtl.Modify(true);

        EnterTestResultDialog.SetRecord(QualityControlLineDtl);
        EnterTestResultDialog.Run();

        //Page.Run(Page::"Enter Test Result Dialog SMK", TempQualityControlLineDtl, TempQualityControlLineDtl."Result Value")
    end;

    procedure CalculateQualityControlLineStatus(var QualityControlLine: Record "Quality Control Line SMK")
    begin
        QualityControlLine.CalcFields("Average Result Value", "Not OK Detail Exist");

        case QualityControlLine."Specification Reference" of
            QualityControlLine."Specification Reference"::Between:
                if (QualityControlLine."Average Result Value" >= QualityControlLine."Min Value") and (QualityControlLine."Average Result Value" <= QualityControlLine."Max Value") then
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Acceptance)
                else
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Rejection);
            QualityControlLine."Specification Reference"::Equal:
                if QualityControlLine."Average Result Value" = QualityControlLine."Exact Value" then
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Acceptance)
                else
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Rejection);
            QualityControlLine."Specification Reference"::Greater:
                if QualityControlLine."Average Result Value" >= QualityControlLine."Min Value" then
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Acceptance)
                else
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Rejection);
            QualityControlLine."Specification Reference"::Less:
                if QualityControlLine."Average Result Value" <= QualityControlLine."Max Value" then
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Acceptance)
                else
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Rejection);
            QualityControlLine."Specification Reference"::Selection:
                if QualityControlLine."Not OK Detail Exist" then
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Rejection)
                else
                    QualityControlLine.Validate("Line Status", QualityControlLine."Line Status"::Acceptance);
        end;

        QualityControlLine.Modify(true);
        CalculateQualityControlHeaderStatus(QualityControlLine)
    end;

    procedure CalculateQualityControlLineDetailStatus(var QualityControlLineDtl: Record "Quality Control Line Dtl. SMK")
    var
        PackageNoInformation: Record "Package No. Information";
        QualityControlLineDtl2: Record "Quality Control Line Dtl. SMK";
    begin
        //QualityControlLine.CalcFields("Average Result Value");

        case QualityControlLineDtl."Specification Reference" of
            QualityControlLineDtl."Specification Reference"::Between:
                if (QualityControlLineDtl."Result Value" >= QualityControlLineDtl."Min Value") and (QualityControlLineDtl."Result Value" <= QualityControlLineDtl."Max Value") then
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Acceptance)
                else
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Rejection);
            QualityControlLineDtl."Specification Reference"::Equal:
                if QualityControlLineDtl."Result Value" = QualityControlLineDtl."Exact Value" then
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Acceptance)
                else
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Rejection);
            QualityControlLineDtl."Specification Reference"::Greater:
                if QualityControlLineDtl."Result Value" >= QualityControlLineDtl."Min Value" then
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Acceptance)
                else
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Rejection);
            QualityControlLineDtl."Specification Reference"::Less:
                if QualityControlLineDtl."Result Value" <= QualityControlLineDtl."Max Value" then
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Acceptance)
                else
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Rejection);
            QualityControlLineDtl."Specification Reference"::Selection:
                if QualityControlLineDtl."Selection Result Value" = QualityControlLineDtl."Selection Value" then
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Acceptance)
                else
                    QualityControlLineDtl.Validate("Line Status", QualityControlLineDtl."Line Status"::Rejection);
        end;

        QualityControlLineDtl.Modify(true);

        if PackageNoInformation.Get(QualityControlLineDtl."Item No.", QualityControlLineDtl."Variant Code", QualityControlLineDtl."Package No.") then
            // If the current line status is Rejection or the package is already in Rejection state, always set to Rejection
            if (QualityControlLineDtl."Line Status" = QualityControlLineDtl."Line Status"::Rejection) or
               (PackageNoInformation."Quality Control Status SMK" = PackageNoInformation."Quality Control Status SMK"::Rejection) then begin
                PackageNoInformation.Validate("Quality Control Status SMK", PackageNoInformation."Quality Control Status SMK"::Rejection);
                PackageNoInformation.Modify(true);
            end else begin
                // Only if current line is accepted and package is not already rejected, check if all are accepted
                QualityControlLineDtl2 := QualityControlLineDtl;
                QualityControlLineDtl.Reset();
                QualityControlLineDtl.SetRange("Document No.", QualityControlLineDtl2."Document No.");
                QualityControlLineDtl.SetRange("Package No.", QualityControlLineDtl2."Package No.");
                QualityControlLineDtl.SetRange("Line Status", QualityControlLineDtl."Line Status"::Rejection);
                if QualityControlLineDtl.IsEmpty() then
                    PackageNoInformation.Validate("Quality Control Status SMK", PackageNoInformation."Quality Control Status SMK"::Acceptance)
                else
                    PackageNoInformation.Validate("Quality Control Status SMK", PackageNoInformation."Quality Control Status SMK"::Rejection);
                PackageNoInformation.Modify(true);
            end;
    end;

    // local procedure UpdatePackageNoInformationQualityControlStatusFromQualityControlLineDetail(var QualityControlLineDtl: Record "Quality Control Line Dtl. SMK")
    // var
    //     PackageNoInformation: Record "Package No. Information";
    //     QualityControlHeader: Record "Quality Control Header SMK";
    //     QualityControlLineDtl2: Record "Quality Control Line Dtl. SMK";
    // begin
    //     if not PackageNoInformation.Get(QualityControlLineDtl."Item No.", QualityControlLineDtl."Variant Code", QualityControlLineDtl."Package No.") then
    //         exit;

    //     QualityControlHeader.Get(QualityControlLineDtl."Document No.");

    //     case QualityControlLineDtl."Line Status" of
    //         QualityControlLineDtl."Line Status"::Rejection:
    //             if QualityControlHeader.Type = QualityControlHeader.Type::Purchase then
    //                 PackageNoInformation."Production Blocked SMK" := true
    //             else
    //                 if QualityControlHeader.Type = QualityControlHeader.Type::Production then
    //                     PackageNoInformation."Sales Blocked SMK" := true;
    //         QualityControlLineDtl."Line Status"::Acceptance:
    //             begin
    //                 QualityControlLineDtl2.SetCurrentKey("Specification Code");
    //                 QualityControlLineDtl2.SetRange("Package No.", QualityControlLineDtl."Package No.");
    //                 QualityControlLineDtl2.SetFilter("Line Status", '<>%1', QualityControlLineDtl2."Line Status"::Acceptance);
    //                 QualityControlLineDtl2.SetFilter("Specification Code", '<>%1', QualityControlLineDtl."Specification Code");
    //             end;
    //     end;
    // end;

    local procedure CalculateQualityControlHeaderStatus(QualityControlLine: Record "Quality Control Line SMK")
    var
        QualityControlHeader: Record "Quality Control Header SMK";
        QualityControlLine2: Record "Quality Control Line SMK";
    begin
        QualityControlHeader.Get(QualityControlLine."Document No.");

        QualityControlLine2.SetRange("Document No.", QualityControlHeader."No.");
        QualityControlLine2.SetFilter("Line Status", '<>%1', QualityControlLine2."Line Status"::Acceptance);
        if QualityControlLine2.IsEmpty() then
            QualityControlHeader.Validate(Status, QualityControlHeader.Status::Acceptance)
        else
            QualityControlHeader.Validate(Status, QualityControlHeader.Status::Rejection);

        QualityControlHeader.Modify(true);
    end;

    procedure CreateQualityControlDocumentFromLotNoInformation(var LotNoInformation: Record "Lot No. Information"; QualityControlType: Enum "Quality Control Type SMK")
    var
        QualityControlHeader: Record "Quality Control Header SMK";
        Item: Record Item;
        ItemCategory: Record "Item Category";
    begin
        Item.Get(LotNoInformation."Item No.");
        ItemCategory.Get(Item."Item Category Code");
        if not ItemCategory."Auto. Quality Control Doc. SMK" then
            exit;

        QualityControlHeader.Init();
        QualityControlHeader.Insert(true);
        QualityControlHeader.Validate(Type, QualityControlType);
        QualityControlHeader.Validate("Item No.", LotNoInformation."Item No.");
        QualityControlHeader.Validate("Variant Code", LotNoInformation."Variant Code");
        QualityControlHeader.Validate("Item Description", LotNoInformation.Description);
        QualityControlHeader.Validate(Date, WorkDate());
        QualityControlHeader.Validate("Lot No.", LotNoInformation."Lot No.");
        QualityControlHeader.Modify(true);

        LotNoInformation.Validate("Certificate Number", QualityControlHeader."No.");
        LotNoInformation.Modify(true);

        PopulateQualityControlLines(QualityControlHeader);
    end;

    procedure OpenQualityControlDocumentAndPopulateQCLineDetailsFromPackageNo(PackageNo: Code[50])
    var
        PackageNoInformation: Record "Package No. Information";
        QualityControlHeader: Record "Quality Control Header SMK";
        QualityControlLine: Record "Quality Control Line SMK";
        QualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
    begin
        SumikaSetup.GetRecordOnce();
        SumikaSetup.TestField("Temp. Quality Control Nos");

        PackageNoInformation.SetRange("Package No.", PackageNo);
        PackageNoInformation.FindFirst();

        QualityControlHeader.Init();
        QualityControlHeader."No." := NoSeries.GetNextNo(SumikaSetup."Temp. Quality Control Nos");
        QualityControlHeader.Insert(true);
        QualityControlHeader.Validate(Type, QualityControlHeader.Type::Production);
        QualityControlHeader.Validate("Item No.", PackageNoInformation."Item No.");
        QualityControlHeader.Validate("Variant Code", PackageNoInformation."Variant Code");
        QualityControlHeader.Validate("Item Description", PackageNoInformation.Description);
        QualityControlHeader.Validate(Date, WorkDate());
        QualityControlHeader.Validate("Lot No.", PackageNoInformation."Lot No. SMK");
        QualityControlHeader."Package No." := PackageNo;
        QualityControlHeader.Modify(true);

        PopulateQualityControlLines(QualityControlHeader);

        QualityControlLine.SetRange("Document No.", QualityControlHeader."No.");
        QualityControlLine.FindSet(false);
        repeat
            QualityControlLineDtl.Init();
            QualityControlLineDtl."Document No." := QualityControlLine."Document No.";
            QualityControlLineDtl."Document Line No." := QualityControlLine."Line No.";
            QualityControlLineDtl.Insert(true);
            QualityControlLineDtl."Package No." := PackageNo;
            QualityControlLineDtl.Validate("Specification Code", QualityControlLine."Specification Code");
            QualityControlLineDtl.Validate("Specification Description", QualityControlLine."Specification Description");
            QualityControlLineDtl.Validate("Specification Reference", QualityControlLine."Specification Reference");
            QualityControlLineDtl.Validate("Min Value", QualityControlLine."Min Value");
            QualityControlLineDtl.Validate("Max Value", QualityControlLine."Max Value");
            QualityControlLineDtl.Validate("Exact Value", QualityControlLine."Exact Value");
            QualityControlLineDtl.Validate("Selection Value", QualityControlLine."Selection Value");
            QualityControlLineDtl.Validate("Unit of Measure Code", QualityControlLine."Unit of Measure Code");
            QualityControlLineDtl.Validate(Standard, QualityControlLine.Standard);
            QualityControlLineDtl.Validate("Item No.", QualityControlHeader."Item No.");
            QualityControlLineDtl.Validate("Variant Code", QualityControlHeader."Variant Code");
            QualityControlLineDtl.Validate("Lot No.", QualityControlHeader."Lot No.");
            QualityControlLineDtl.Modify(true);
        until QualityControlLine.Next() = 0;

        Page.Run(Page::"Package Quality Control Doc.", QualityControlHeader);
    end;

    procedure PostQualityControlDocument(var SourceQualityControlHeader: Record "Quality Control Header SMK")
    var
        PackageNoInformation: Record "Package No. Information";
        TargetQualityControlHeader: Record "Quality Control Header SMK";
        SourceQualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
        TargetQualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
        TargetQualityControlLine: Record "Quality Control Line SMK";
    begin
        SourceQualityControlHeader.TestField(Posted, false);

        PackageNoInformation.SetRange("Package No.", SourceQualityControlHeader."Package No.");
        PackageNoInformation.FindFirst();

        TargetQualityControlHeader.SetRange("Lot No.", PackageNoInformation."Lot No. SMK");
        TargetQualityControlHeader.SetFilter("Package No.", '<>%1', SourceQualityControlHeader."Package No.");
        TargetQualityControlHeader.SetRange(Posted, false);
        TargetQualityControlHeader.FindFirst();

        if not ConfirmManagement.GetResponseOrDefault('Do you want to post the Quality Control Document?', true) then
            exit;

        SourceQualityControlLineDtl.SetRange("Document No.", SourceQualityControlHeader."No.");
        SourceQualityControlLineDtl.FindSet(true);
        repeat
            if SourceQualityControlLineDtl."Specification Reference" <> SourceQualityControlLineDtl."Specification Reference"::Selection then begin
                if SourceQualityControlLineDtl."Result Value" <> 0 then begin
                    TargetQualityControlLineDtl.Init();
                    TargetQualityControlLineDtl.TransferFields(SourceQualityControlLineDtl);
                    TargetQualityControlLineDtl."Document No." := TargetQualityControlHeader."No.";
                    TargetQualityControlLineDtl."Line No." := 0;
                    TargetQualityControlLineDtl."QC Control Quantity" := SourceQualityControlHeader."QC Control Quantity";
                    TargetQualityControlLineDtl."Source Q.C. Document No." := SourceQualityControlHeader."No.";
                    TargetQualityControlLineDtl.Insert(true);
                    TargetQualityControlLine.Get(TargetQualityControlLineDtl."Document No.", TargetQualityControlLineDtl."Document Line No.");
                    CalculateQualityControlLineDetailStatus(TargetQualityControlLineDtl);
                    CalculateQualityControlLineStatus(TargetQualityControlLine);
                end
            end
            else
                //if SourceQualityControlLineDtl."Specification Reference" = SourceQualityControlLineDtl."Specification Reference"::Selection then
                if SourceQualityControlLineDtl."Selection Result Value" <> SourceQualityControlLineDtl."Selection Result Value"::" " then begin
                    TargetQualityControlLineDtl.Init();
                    TargetQualityControlLineDtl.TransferFields(SourceQualityControlLineDtl);
                    TargetQualityControlLineDtl."Document No." := TargetQualityControlHeader."No.";
                    TargetQualityControlLineDtl."Line No." := 0;
                    TargetQualityControlLineDtl."QC Control Quantity" := SourceQualityControlHeader."QC Control Quantity";
                    TargetQualityControlLineDtl."Source Q.C. Document No." := SourceQualityControlHeader."No.";
                    TargetQualityControlLineDtl.Insert(true);
                    TargetQualityControlLine.Get(TargetQualityControlLineDtl."Document No.", TargetQualityControlLineDtl."Document Line No.");
                    CalculateQualityControlLineDetailStatus(TargetQualityControlLineDtl);
                    CalculateQualityControlLineStatus(TargetQualityControlLine);
                end;
        until SourceQualityControlLineDtl.Next() = 0;

        SourceQualityControlHeader.Posted := true;
        SourceQualityControlHeader.Modify(true);

        Message('Quality Control Document %1 has been posted to %2', SourceQualityControlHeader."No.", TargetQualityControlHeader."No.");
    end;

    var
        SumikaSetup: Record "Sumika Setup SMK";
        NoSeries: Codeunit "No. Series";
        ConfirmManagement: Codeunit "Confirm Management";
}