report 60002 "Quality Control SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control';
    UsageCategory = ReportsAndAnalysis;
    RDLCLayout = 'QualityControl.rdl';
    dataset
    {
        dataitem(QualityControlHeaderSMK; "Quality Control Header SMK")
        {
            column(No; "No.")
            {
            }
            column(ItemNo; "Item No.")
            {
            }
            column(VariantCode; "Variant Code")
            {
            }
            column(ItemDescription; "Item Description")
            {
            }
            column(Status; Status)
            {
            }
            column(LotNo; "Lot No.")
            {
            }
            column(SystemCreatedAt; SystemCreatedAt)
            {
            }
            column(ResponsibleUserID; SumikaBasicFunctions.GetFullUserNameFromUserId("Responsible User ID"))
            {
            }
            column(QualityEmployeeUserID; SumikaBasicFunctions.GetFullUserNameFromUserId("Quality Employee User ID"))
            {
            }
            dataitem("Quality Control Line SMK"; "Quality Control Line SMK")
            {
                DataItemLink = "Document No." = field("No.");
                DataItemTableView = where("Show On Report" = const(true));
                CalcFields = "Average Result Value";
                column(SpecificationCode_QualityControlLineSMK; "Specification Code")
                {
                }
                column(SpecificationDescription_QualityControlLineSMK; "Specification Description")
                {
                }
                column(Standard_QualityControlLineSMK; Standard)
                {
                }
                column(AverageResultValue_QualityControlLineSMK; LayoutResult)
                {
                }
                column(UnitofMeasureCode_QualityControlLineSMK; "Unit of Measure Code")
                {
                }
                column(SelectionValue_QualityControlLineSMK; "Selection Value")
                {
                }
                column(ExactValue_QualityControlLineSMK; "Exact Value")
                {
                }
                column(MaxValue_QualityControlLineSMK; "Max Value")
                {
                }
                column(MinValue_QualityControlLineSMK; "Min Value")
                {
                }
                column(SpecificationReference_QualityControlLineSMK; "Specification Reference")
                {
                }
                column(LineStatus_QualityControlLineSMK; "Line Status")
                {
                }
                trigger OnAfterGetRecord()
                begin
                    if "Quality Control Line SMK"."Specification Reference" = "Quality Control Line SMK"."Specification Reference"::Selection then
                        LayoutResult := Format("Quality Control Line SMK"."Selection Result Value")
                    else begin
                        RoundedDecimalValue := Round("Average Result Value", 0.01);
                        LayoutResult := Format(RoundedDecimalValue);
                    end;
                end;
            }
            dataitem("Company Information"; "Company Information")
            {

                column(Name_CompanyInformation; Name)
                {
                }
                column(Address_CompanyInformation; Address)
                {
                }
                column(Address2_CompanyInformation; "Address 2")
                {
                }
                column(City_CompanyInformation; City)
                {
                }
                column(County_CompanyInformation; County)
                {
                }
                column(PostCode_CompanyInformation; "Post Code")
                {
                }
                column(CountryRegionCode_CompanyInformation; "Country/Region Code")
                {
                }
                column(PhoneNo_CompanyInformation; "Phone No.")
                {
                }
            }
            dataitem(Customer; Customer)
            {
                DataItemLink = "No." = field("Customer No.");
                column(Name_Customer; Name)
                {
                }
                column(Address_Customer; Address)
                {
                }
                column(Address2_Customer; "Address 2")
                {
                }
                column(City_Customer; City)
                {
                }
                column(County_Customer; County)
                {
                }
                column(PostCode_Customer; "Post Code")
                {
                }
                column(CountryRegionCode_Customer; "Country/Region Code")
                {
                }
            }
            dataitem(SalesShipmentHeader; "Sales Shipment Header")
            {
                DataItemLink = "No." = field("Shipment No.");
                column(No_SalesShipmentHeader; "No.")
                {
                }
                column(OrderNo_SalesShipmentHeader; "Order No.")
                {
                }
            }
        }
    }
    var
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
        LayoutResult: Text;
        RoundedDecimalValue: Decimal;
}