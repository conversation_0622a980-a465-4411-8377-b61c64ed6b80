tableextension 60016 "Item Ledger Entry SMK" extends "Item Ledger Entry"
{
    fields
    {
        field(60000; "Parent Package No. SMK"; Code[50])
        {
            Caption = 'Parent Package No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Parent Package No. SMK" where("Package No." = field("Package No.")));
            AllowInCustomizations = Always;
        }
        field(60001; "Item Category Code FF SMK"; Code[20])
        {
            Caption = 'Item Category Code FlowField';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Item Category Code" where("No." = field("Item No.")));
            AllowInCustomizations = Always;
        }
        field(60002; "Vendor Lot No. SMK"; Code[50])
        {
            Caption = 'Vendor Lot No.';
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = lookup("Lot No. Information"."Vendor Lot No. SMK" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Lot No." = field("Lot No.")));
            CalcFormula = lookup("Package No. Information"."Vendor Lot No. SMK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Vendor Lot No. field.';
        }
        field(60003; "Cost Amt. (Actual) by Date SMK"; Decimal)
        {
            AutoFormatType = 1;
            CalcFormula = sum("Value Entry"."Cost Amount (Actual)" where("Item Ledger Entry No." = field("Entry No."), "Posting Date" = field("Date Filter SMK")));
            Caption = 'Cost Amount (Actual) by Date';
            Editable = false;
            FieldClass = FlowField;
            ToolTip = 'Specifies the value of the Cost Amount (Actual) by Date field.';
        }
        field(60004; "Date Filter SMK"; Date)
        {
            Caption = 'Date Filter';
            FieldClass = FlowFilter;
        }
        field(60005; "Include In Mail SMK"; Boolean)
        {
            Caption = 'Include In Mail FlowField';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Include In Mail SMK" where("No." = field("Item No.")));
            AllowInCustomizations = Always;
        }
    }

    trigger OnAfterInsert()
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        if Rec."Package No." <> '' then
            if PackageNoInformation.Get(Rec."Item No.", Rec."Variant Code", Rec."Package No.") then begin
                PackageNoInformation.CalcFields(Inventory);
                if PackageNoInformation.Inventory = 0 then
                    if PackageNoInformation."Parent Package No. SMK" <> '' then begin
                        PackageNoInformation.Validate("Parent Package No. SMK", '');
                        PackageNoInformation.Modify(true);
                    end;
            end;
    end;

}