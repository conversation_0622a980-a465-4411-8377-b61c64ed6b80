pageextension 60001 "Released Prod. Order Lines SMK" extends "Released Prod. Order Lines"
{
    layout
    {
        modify("Routing No.")
        {
            Visible = true;
        }
        modify("Variant Code")
        {
            Visible = true;
        }
        modify("Location Code")
        {
            Visible = true;
        }
        modify("Bin Code")
        {
            Visible = true;
        }
        addlast(Control1)
        {
            field("Lot No. SMK"; Rec."Lot No. SMK")
            {
                ApplicationArea = All;
            }
            field("Parent Package Count SMK"; Rec."Parent Package Count SMK")
            {
                ApplicationArea = All;
            }
            field("Package Count SMK"; Rec."Package Count SMK")
            {
                ApplicationArea = All;
            }
        }
        addafter("Description 2")
        {
            field("Packaging Type SMK"; Rec."Packaging Type SMK")
            {
                ApplicationArea = All;
            }
        }
    }
    actions
    {
        addfirst(processing)
        {
            action("Assign Lot No. SMK")
            {
                ApplicationArea = All;
                Caption = 'Assign &Lot No.';
                Image = Lot;
                ToolTip = 'Automatically assign the required lot numbers from predefined number series.';

                trigger OnAction()
                begin
                    SumikaProductionMgt.AssignLotNo(Rec);
                end;
            }
            action("CreateSinglePackage SMK")
            {
                ApplicationArea = All;
                Caption = 'Create LPN';
                Image = RegisterPutAway;
                ToolTip = 'Executes the Create LPN action.';
                trigger OnAction()
                begin
                    SumikaProductionMgt.PopulateAndOpenPackageCreation(Rec, Enum::"Package Creation Type SMK"::Single);
                end;
            }
            action("CreateMultiplePackages SMK")
            {
                ApplicationArea = All;
                Caption = 'Create multi-bag LPN';
                Image = CreatePutawayPick;
                ToolTip = 'Executes the Create multi-bag LPNe action.';
                trigger OnAction()
                begin
                    SumikaProductionMgt.PopulateAndOpenPackageCreation(Rec, Enum::"Package Creation Type SMK"::Multiple);
                end;
            }
            action("CreateMultiplePackagesWithoutPalette SMK")
            {
                ApplicationArea = All;
                Caption = 'Create Multiple LPN';
                Image = ItemTracking;
                ToolTip = 'Executes the Create Multiple LPN action.';
                trigger OnAction()
                begin
                    SumikaProductionMgt.PopulateAndOpenPackageCreation(Rec, Enum::"Package Creation Type SMK"::"Multiple without Palette");
                end;
            }
            action("OpenQualityControlDocument SMK")
            {
                ApplicationArea = All;
                Caption = 'Open Quality Control Document';
                Image = TaskQualityMeasure;
                ToolTip = 'Executes the Open Quality Control Document action.';
                trigger OnAction()
                var
                    QualityControlHeader: Record "Quality Control Header SMK";
                begin
                    QualityControlHeader.SetRange("Lot No.", Rec."Lot No. SMK");
                    if not QualityControlHeader.FindFirst() then
                        exit;

                    Page.Run(Page::"Quality Control SMK", QualityControlHeader);
                end;
            }
        }
    }
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}