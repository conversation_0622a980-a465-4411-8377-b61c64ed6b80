pageextension 60042 "Consumption Journal SMK" extends "Consumption Journal"
{
    layout
    {
        addafter(Quantity)
        {
            field("TransferredQuantity SMK"; SumikaBasicFunctions.CalculateTransferredQtyFromItemJournalLine(Rec))
            {
                ApplicationArea = All;
                Caption = 'Transferred Quantity';
                ToolTip = 'Specifies the value of the Transferred Quantity field.';
            }
        }
        addafter(CurrentJnlBatchName)
        {
            field("TotalKG SMK"; SumikaProductionMgt.CalculateTotalKGFromItemJournal(Rec))
            {
                ApplicationArea = All;
                Caption = 'Total KG';
                ToolTip = 'Specifies the value of the Total KG field.';
            }
        }
    }
    var
        SumikaBasicFunctions: Codeunit "Sumika Basic Functions SMK";
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}