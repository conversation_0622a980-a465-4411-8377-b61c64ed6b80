codeunit 60012 "Copy Quality Control Spec. SMK"
{
    TableNo = "Quality Control Spec. SMK";
    trigger OnRun()
    begin
        if NewQualityControlSpec.Get(Rec.Code) then begin

            NewQualityControlSpec.TransferFields(Rec);
            NewQualityControlSpec.Modify(true);
        end
        else begin
            NewQualityControlSpec.Init();
            NewQualityControlSpec.TransferFields(Rec);
            NewQualityControlSpec.Insert(true);
        end;
    end;

    var
        NewQualityControlSpec: Record "Quality Control Spec. SMK";
}