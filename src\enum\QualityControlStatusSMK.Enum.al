enum 60001 "Quality Control Status SMK"
{
    Extensible = true;

    value(0; " ")
    {
        Caption = ' ', Locked = true;
    }
    value(1; "Input Pending")
    {
        Caption = 'Input Pending';
    }
    value(2; Acceptance)
    {
        Caption = 'Acceptance';
    }
    value(3; "Conditional Acceptance")
    {
        Caption = 'Conditional Acceptance';
    }
    value(4; Rejection)
    {
        Caption = 'Rejection';
    }
}