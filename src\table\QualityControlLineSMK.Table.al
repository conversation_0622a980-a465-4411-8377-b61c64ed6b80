table 60011 "Quality Control Line SMK"
{
    Caption = 'Quality Control Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(3; "Specification Code"; Code[10])
        {
            Caption = 'Specification Code';
            ToolTip = 'Specifies the value of the Specification Code field.';
        }
        field(4; "Specification Description"; Text[100])
        {
            Caption = 'Specification Description';
            ToolTip = 'Specifies the value of the Specification Description field.';
        }
        field(5; "Specification Reference"; Enum "Q.C. Spec. Reference Type SMK")
        {
            Caption = 'Specification Reference';
            ToolTip = 'Specifies the value of the Specification Reference field.';
        }
        field(6; "Min Value"; Decimal)
        {
            Caption = 'Min Value';
            ToolTip = 'Specifies the value of the Min Value field.';
        }
        field(7; "Max Value"; Decimal)
        {
            Caption = 'Max Value';
            ToolTip = 'Specifies the value of the Max Value field.';
        }
        field(8; "Exact Value"; Decimal)
        {
            Caption = 'Exact Value';
            ToolTip = 'Specifies the value of the Exact Value field.';
        }
        field(9; "Selection Value"; Enum "Quality Control Selection SMK")
        {
            Caption = 'Selection Value';
            ToolTip = 'Specifies the value of the Selection Value field.';
        }
        field(10; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(11; Standard; Text[30])
        {
            Caption = 'Standard';
            Editable = false;
            ToolTip = 'Specifies the value of the Standard field.';
        }
        field(12; "Average Result Value"; Decimal)
        {
            Caption = 'Average Result Value';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = average("Quality Control Line Dtl. SMK"."Result Value" where("Document No." = field("Document No."), "Document Line No." = field("Line No.")));
            ToolTip = 'Specifies the value of the Average Result Value field.';
        }
        field(13; "Selection Result Value"; Enum "Quality Control Selection SMK")
        {
            Caption = 'Selection Result Value';
            ToolTip = 'Specifies the value of the Selection Result Value field.';
            trigger OnValidate()
            var
                UseEnterResultPageErr: Label 'You have to enter Selection Result Value in Enter Result page.';
            begin
                Error(UseEnterResultPageErr);
                //SumikaQualityCtrlMgt.CalculateQualityControlLineStatus(Rec);
            end;
        }
        field(14; "Line Status"; Enum "Quality Control Status SMK")
        {
            Caption = 'Line Status';
            InitValue = "Input Pending";
            ToolTip = 'Specifies the value of the Line Status field.';
        }
        field(15; "Show On Report"; Boolean)
        {
            Caption = 'Show On Report';
            InitValue = true;
            ToolTip = 'Specifies the value of the Show On Report field.';
        }
        field(16; "Not OK Detail Exist"; Boolean)
        {
            Caption = 'Not OK Detail Exist';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = exist("Quality Control Line Dtl. SMK" where("Document No." = field("Document No."), "Document Line No." = field("Line No."), "Selection Result Value" = filter(" " | "Not OK")));
            ToolTip = 'Specifies the value of the Not OK Detail Exist field.';
        }
    }

    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        QualityControlLine: Record "Quality Control Line SMK";
    begin
        QualityControlLine.SetRange("Document No.", Rec."Document No.");
        if QualityControlLine.FindLast() then
            Rec."Line No." := QualityControlLine."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;

    trigger OnDelete()
    var
        QualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
    begin
        QualityControlLineDtl.SetRange("Document No.", Rec."Document No.");
        QualityControlLineDtl.SetRange("Document Line No.", Rec."Line No.");
        QualityControlLineDtl.DeleteAll(true);
    end;

    // var
    //     SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
}