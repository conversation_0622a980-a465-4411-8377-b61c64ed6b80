page 60012 "Quality Control Subpage SMK"
{
    ApplicationArea = All;
    Caption = 'Quality Control Subpage';
    PageType = ListPart;
    SourceTable = "Quality Control Line SMK";
    RefreshOnActivate = true;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Specification Code"; Rec."Specification Code")
                {
                    Editable = false;
                }
                field("Specification Description"; Rec."Specification Description")
                {
                    Editable = false;
                }
                field("Specification Reference"; Rec."Specification Reference")
                {
                    Editable = false;
                }
                field("Min Value"; Rec."Min Value")
                {
                    Editable = false;
                }
                field("Max Value"; Rec."Max Value")
                {
                    Editable = false;
                }
                field("Exact Value"; Rec."Exact Value")
                {
                    Editable = false;
                }
                field("Selection Value"; Rec."Selection Value")
                {
                    Editable = false;
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    Editable = false;
                }
                field(Standard; Rec.Standard)
                {
                    Editable = false;
                }
                field("Selection Result Value"; Rec."Selection Result Value")
                {
                    Visible = false;
                }
                field("Average Result Value"; Rec."Average Result Value")
                {
                }
                field("Not OK Detail Exist"; Rec."Not OK Detail Exist")
                {
                }
                field("Line Status"; Rec."Line Status")
                {
                    Editable = false;
                }
                field("Show On Report"; Rec."Show On Report")
                {
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(EnterTestResult)
            {
                ApplicationArea = All;
                Caption = 'Enter Test Result';
                Image = TestReport;
                ToolTip = 'Executes the Enter Test Result action.';

                trigger OnAction()
                begin
                    SumikaQualityCtrlMgt.CreateQualityControlLineDetailFromQualityControlLine(Rec);
                    CurrPage.Update();
                end;
            }
        }
    }
    var
        SumikaQualityCtrlMgt: Codeunit "Sumika Quality Ctrl. Mgt. SMK";
}