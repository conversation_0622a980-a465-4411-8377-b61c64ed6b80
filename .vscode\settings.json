{
    "alOutline.additionalMandatoryAffixesPatterns": [
        " INF",
        " SMK"
    ],
    "alOutline.defaultDataClassification": "CustomerContent",
    "alOutline.enableCodeCopFixes": true,
    "alOutline.fixCaseRemovesQuotesFromDataTypeIdentifiers": true,
    "alOutline.fixCodeCopMissingParenthesesOnSave": true,
    "alOutline.noEmptyLinesAtTheEndOfWizardGeneratedFiles": true,
    "alOutline.openDefinitionInNewTab": true,
    "linterCop.load-pre-releases": true,
    "alVarHelper.ignoreALSuffix": "SMK",
    "alNavigator.ignoreALSuffix": "SMK",
    "CRS.OnSaveAlFileAction": "Reorganize",
    "CRS.ObjectNameSuffix": " SMK",
    "al.codeAnalyzers": [
        "${CodeCop}",
        "${UICop}",
        "${PerTenantExtensionCop}",
        "${analyzerFolder}BusinessCentral.LinterCop.dll"
    ],
    "al.ruleSetPath": "infotek.ruleset.json",
    "editor.codeActionsOnSave": {
    },
    "editor.snippetSuggestions": "bottom",
    "ALTB.snippetTargetLanguage": "TRK",
    "xliffSync.snippetTargetLanguage": "TRK",
    "alOutline.activeBuildConfiguration": "",
}