# Consumption Journal Manual Output Enhancement

## Overview
This enhancement adds a new "Manual Output" option to the Calc. Consumption report, allowing users to specify a manual output quantity for consumption calculations instead of relying on actual or expected output.

## Changes Made

### 1. Report Extension: CalcConsumptionSMK.ReportExt.al
- **File**: `src/reportextension/CalcConsumptionSMK.ReportExt.al`
- **Purpose**: Extends the standard "Calc. Consumption" report with manual output functionality

#### New Fields Added:
- **Use Manual Output**: Boolean field to enable/disable manual output mode
- **Manual Output Quantity**: Decimal field to specify the manual output quantity

#### Features:
- Manual Output Quantity field is only enabled when "Use Manual Output" is checked
- Validation ensures Manual Output Quantity cannot be negative
- Error handling prevents running the report with invalid manual quantities
- Automatic cleanup of parameters after report execution

### 2. Codeunit Enhancement: SumikaProductionMgtSMK.Codeunit.al
- **File**: `src/codeunit/SumikaProductionMgtSMK.Codeunit.al`
- **Purpose**: Enhanced to support manual output quantity calculations

#### New Global Variables:
- `GlobalManualOutputQty`: Decimal - Stores the manual output quantity
- `UseManualOutput`: Boolean - Flag to indicate manual output mode

#### New Procedure:
- `SetManualOutputParameters(UseManual: Boolean; ManualQty: Decimal)`: Sets the manual output parameters

#### Enhanced Event Subscriber:
- **Event**: `Calc. Consumption_OnBeforeGetNeededQty`
- **Enhancement**: Now checks for manual output mode first, then falls back to existing GlobalOutputQty logic
- **Priority**: Manual output takes precedence over automatic calculations

## Usage Instructions

### For End Users:
1. Open the Consumption Journal
2. Click "Calc. Consumption" action
3. In the request page:
   - Check "Use Manual Output" checkbox
   - Enter the desired quantity in "Manual Output Quantity" field
   - Click OK to run the calculation

### Calculation Logic:
When "Use Manual Output" is enabled:
```
NeededQty = ProdOrderComponent."Quantity per" × Manual Output Quantity
```

When "Use Manual Output" is disabled:
- Falls back to existing logic using GlobalOutputQty (for backward compatibility)
- Standard BC calculation based on CalcBasedOn option (Actual Output/Expected Output)

## Backward Compatibility
- All existing functionality remains unchanged
- Existing code that uses GlobalOutputQty continues to work
- Manual output mode only activates when explicitly enabled by the user

## Technical Implementation Details

### Event Subscriber Priority:
1. **Manual Output** (highest priority) - when UseManualOutput = true and GlobalManualOutputQty > 0
2. **Global Output Qty** (medium priority) - when GlobalOutputQty > 0
3. **Standard BC Logic** (lowest priority) - default behavior

### Error Handling:
- Prevents negative manual output quantities
- Requires positive manual output quantity when manual mode is enabled
- Automatic parameter cleanup to prevent state persistence issues

## Testing Recommendations
1. Test with manual output enabled and various quantities
2. Test with manual output disabled (should behave as before)
3. Test error scenarios (negative quantities, zero quantities)
4. Verify backward compatibility with existing consumption journal workflows
5. Test parameter cleanup after report execution

## Files Modified
- `src/reportextension/CalcConsumptionSMK.ReportExt.al` (new file)
- `src/codeunit/SumikaProductionMgtSMK.Codeunit.al` (enhanced)

## Related Jira Issue
- **Issue**: Consumption Journal enhancement request
- **Description**: Add Manual Output option to Calc. Consumption report
