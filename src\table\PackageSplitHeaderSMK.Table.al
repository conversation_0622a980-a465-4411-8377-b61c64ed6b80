table 60021 "Package Split Header SMK"
{
    DataClassification = CustomerContent;
    Caption = 'Package Split Header';
    DrillDownPageId = "Package Split List SMK";
    LookupPageId = "Package Split List SMK";
    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            DataClassification = SystemMetadata;
            Editable = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            var
                FlexatiSetup: Record "Sumika Setup SMK";
                NoSeriesManagement: Codeunit "No. Series";
            begin
                if "No." <> xRec."No." then begin
                    FlexatiSetup.Get();
                    NoSeriesManagement.TestManual(FlexatiSetup."Package Split No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            Editable = false;
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(3; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
            trigger OnValidate()
            begin
                PackageSplitManagement.ProcessPackageNo(Rec);
            end;
        }
        field(4; Completed; Boolean)
        {
            Caption = 'Completed';
            Editable = false;
            ToolTip = 'Specifies the value of the Completed field.';
        }
        field(5; "Source Package Quantity"; Decimal)
        {
            Caption = 'Source Package Quantity';
            Editable = false;
            ToolTip = 'Specifies the value of the Total Quantity to Split field.';
        }
        field(6; "New Package Quantity"; Decimal)
        {
            Caption = 'New Package Quantity';
            DecimalPlaces = 0 : 4;
            ToolTip = 'Specifies the value of the Total Split Quantity field.';
            // Editable = false;
            // FieldClass = FlowField;
            // CalcFormula = sum("Package Split Line SMK".Quantity where("Document No." = field("No.")));

            trigger OnValidate()
            var
                ZeroQtyErr: Label 'New package quantity cannot be 0.';
                ZeroQtyTurkishErr: Label 'Yeni paket miktarı 0 olamaz.';
            begin
                if "New Package Quantity" <= 0 then
                    Error(ZeroQtyErr);
            end;
        }
        // field(7; "Requested Hose Lenght"; Decimal)
        // {
        //     Caption = 'Requested Hose Lenght';
        //     Editable = false;
        // }
        field(7; "Source Package Remaining Qty."; Decimal)
        {
            DecimalPlaces = 0 : 4;
            Caption = 'Source Package Remaining Quantity';
            Editable = false;
            ToolTip = 'Specifies the value of the Source Package Remaining Quantity field.';
        }

        field(107; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            TableRelation = "No. Series";
            DataClassification = SystemMetadata;
            AllowInCustomizations = Never;
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        SumikaSetup: Record "Sumika Setup SMK";
        NoSeriesManagement: Codeunit "No. Series";
    begin
        // if "No." = '' then begin
        //     SumikaSetup.Get();
        //     SumikaSetup.TestField("Package Split No. Series");
        //     NoSeriesManagement.InitSeries(SumikaSetup."Package Split No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            SumikaSetup.Get();
            SumikaSetup.TestField("Package Split No. Series");
            "No. Series" := SumikaSetup."Package Split No. Series";
            if NoSeriesManagement.AreRelated(SumikaSetup."Package Split No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

        "Posting Date" := WorkDate();
    end;

    trigger OnDelete()
    var
        PackageSplitLine: Record "Package Split Line SMK";
    begin
        TestField(Completed, false);

        PackageSplitLine.SetRange("Document No.", Rec."No.");
        PackageSplitLine.DeleteAll(true);
    end;

    var
        PackageSplitManagement: Codeunit "Package Split Management SMK";
}