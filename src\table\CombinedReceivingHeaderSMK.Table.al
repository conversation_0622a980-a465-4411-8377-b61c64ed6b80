table 60000 "Combined Receiving Header SMK"
{
    Caption = 'Combined Receiving Header';
    LookupPageId = "Combined Receiving List SMK";
    DrillDownPageId = "Combined Receiving List SMK";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            NotBlank = false;
            ToolTip = 'Specifies the value of the No. field.';
            trigger OnValidate()
            begin
                Clear(NoSeriesManagement);
                if "No." <> xRec."No." then begin
                    SumikaSetup.Get();
                    NoSeriesManagement.TestManual(SumikaSetup."Combined Receiving No. Series");
                    "No. Series" := '';
                end;
            end;
        }
        // field(2; "Shelf No."; Code[20])
        // {
        //     Caption = 'Shelf No.';

        // }
        field(3; "Created At"; DateTime)
        {
            Caption = 'Created At';
            Editable = false;
            ToolTip = 'Specifies the value of the Created At field.';
        }
        field(4; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            ToolTip = 'Specifies the value of the Created By field.';
        }
        field(5; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            AllowInCustomizations = Never;
        }
        field(6; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the value of the Posting Date field.';
        }
        field(7; Status; Enum "Combined Receiving Status SMK")
        {
            Caption = 'Status';
            Editable = false;
            ToolTip = 'Specifies the value of the Status field.';
        }
        field(8; "Vendor No."; Code[20])
        {
            Caption = 'Vendor No.';
            TableRelation = Vendor."No.";
            ToolTip = 'Specifies the value of the Vendor No. field.';
            trigger OnValidate()
            var
                Vendor: Record Vendor;
            begin
                Vendor.Get("Vendor No.");
                "Vendor Name" := Vendor.Name;
            end;
        }
        field(9; "Vendor Name"; Text[100])
        {
            Caption = 'Vendor Name';
            Editable = false;
            ToolTip = 'Specifies the value of the Vendor Name field.';
        }
        // field(10; "Total Package Count"; Integer)
        // {
        //     Caption = 'Total Package Count';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = count("Combined Receiving Line Detail" where("Document No." = field("No.")));
        // }
        // field(11; "Total Received Package Count"; Integer)
        // {
        //     Caption = 'Total Received Package Count';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = count("Combined Receiving Line Detail" where("Document No." = field("No."), Received = const(true)));
        // }
#pragma warning disable AA0232
        field(12; "Total Package Quantity"; Integer)
#pragma warning restore AA0232
        {
            Caption = 'Total Package Quantity';
            FieldClass = FlowField;
            CalcFormula = count("CombinedReceivingLineDtl SMK" where("Document No." = field("No.")));
            Editable = false;
            ToolTip = 'Specifies the value of the Total Received Quantity field.';
        }
        field(13; "Vendor Shipment No."; Code[35])
        {
            Caption = 'Vendor Shipment No.';
            ToolTip = 'Specifies the value of the Vendor Receipt No. field.';
            trigger OnValidate()
            var
                CombinedReceivingHeader: Record "Combined Receiving Header SMK";
                VendorShipmentNoAlreadyUsedErr: Label 'Vendor Shipment No.: %1 is already used in Combined Receiving No.: %2', Comment = '%1="Combined Receiving Header SMK"."Vendor Shipment No."; %2="Combined Receiving Header SMK"."No."';
            begin
                CombinedReceivingHeader.SetRange("Vendor No.", Rec."Vendor No.");
                CombinedReceivingHeader.SetRange("Vendor Shipment No.", Rec."Vendor Shipment No.");
                if CombinedReceivingHeader.FindFirst() then
                    Error(VendorShipmentNoAlreadyUsedErr, Rec."Vendor Shipment No.", CombinedReceivingHeader."No.");
            end;
        }
        field(14; "Purchase Invoice No."; Code[20])
        {
            Caption = 'Purchase Invoice No.';
            Editable = false;
            AllowInCustomizations = Always;
        }
        field(2; "Purchase Order No."; Code[20])
        {
            Caption = 'Purchase Order No.';
            TableRelation = "Purchase Header"."No." where("Document Type" = const(Order), "Buy-from Vendor No." = field("Vendor No."), Status = const(Released));
            ToolTip = 'Specifies the value of the Purchase Order No. field.';
        }
    }
    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(Key2; "Vendor Shipment No.")
        {
        }
    }
    trigger OnInsert()
    begin
        Clear(NoSeriesManagement);
        // if "No." = '' then begin
        //     SumikaSetup.Get();
        //     SumikaSetup.TestField("Combined Receiving No. Series");
        //     NoSeriesManagement.InitSeries(SumikaSetup."Combined Receiving No. Series", xRec."No. Series", 0D, "No.", "No. Series");
        // end;

        if "No." = '' then begin
            SumikaSetup.Get();
            SumikaSetup.TestField("Combined Receiving No. Series");
            "No. Series" := SumikaSetup."Combined Receiving No. Series";
            if NoSeriesManagement.AreRelated(SumikaSetup."Combined Receiving No. Series", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeriesManagement.GetNextNo("No. Series");
        end;

        "Created By" := CopyStr(UserId(), 1, MaxStrLen("Created By"));
        "Created At" := CurrentDateTime();
        //"Posting Date" := Today;
    end;

    trigger OnDelete()
    var
        CombinedReceivingLine: Record "Combined Receiving Line SMK";
        CombinedReceivingLineDetail: Record "CombinedReceivingLineDtl SMK";
    begin
        Rec.TestField(Status, Rec.Status::New);

        CombinedReceivingLine.SetRange("Document No.", Rec."No.");
        CombinedReceivingLine.DeleteAll(true);

        CombinedReceivingLineDetail.SetRange("Document No.", Rec."No.");
        CombinedReceivingLineDetail.DeleteAll(true);
    end;

    var
        SumikaSetup: Record "Sumika Setup SMK";
        NoSeriesManagement: Codeunit "No. Series";
}
