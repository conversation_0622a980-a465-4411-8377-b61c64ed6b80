page 60017 "Package Transfer Order SMK"
{
    ApplicationArea = All;
    Caption = 'Package Transfer Order';
    PageType = Document;
    SourceTable = "Package Transfer Header SMK";
    UsageCategory = None;


    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus SMK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                    QuickEntry = false;
                }
                field("Location Filter"; Rec."Location Filter")
                {
                    //Visible = false;
                    //Importance = Additional;
                }
                field("Transfer-to Code"; Rec."Transfer-to Code")
                {
                    QuickEntry = false;
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                    end;

                    trigger OnLookup(var Text: Text): Boolean
                    var
                        Location: Record Location;
                    begin
                        Location.SetFilter(Code, Rec."Location Filter");

                        if Page.RunModal(0, Location) = Action::LookupOK then
                            Rec.Validate("Transfer-to Code", Location.Code);
                    end;
                }

                field("Transfer-To Bin Code"; Rec."Transfer-To Bin Code")
                {
                    QuickEntry = false;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    QuickEntry = false;
                }
                field("Production Order No.1"; Rec."Production Order No.")
                {
                    QuickEntry = false;
                    Visible = TransferringToProdOrder;
                    ShowMandatory = TransferringToProdOrder;
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        ProductionOrder: Record "Production Order";
                    begin
                        ProductionOrder.SetFilter("No.", Rec."Location Filter");

                        if Page.RunModal(Page::"Released Production Orders", ProductionOrder) = Action::LookupOK then
                            Rec.Validate("Production Order No.", ProductionOrder."No.");
                    end;
                }
                field(Posted; Rec.Posted)
                {
                    QuickEntry = false;
                    Editable = false;
                }
                field("Total Transfer Quantity"; Rec."Total Transfer Quantity")
                {
                }

            }
            group(Production)
            {
                Caption = 'Production';
                field("Transferring to Prod. Location"; Rec."Transferring to Prod. Location")
                {
                    QuickEntry = false;
                }
                field("Production Order No."; Rec."Production Order No.")
                {
                    QuickEntry = false;
                    ShowMandatory = TransferringToProdOrder;
                    trigger OnLookup(var Text: Text): Boolean
                    var
                        ProductionOrder: Record "Production Order";
                    begin
                        ProductionOrder.SetFilter("No.", Rec."Location Filter");

                        if Page.RunModal(Page::"Released Production Orders", ProductionOrder) = Action::LookupOK then
                            Rec.Validate("Production Order No.", ProductionOrder."No.");
                    end;
                }
            }
            group(BarcodeReadingArea)
            {
                Caption = 'Barcode Reading';
                field(Barcode; Rec.Barcode)
                {
                    QuickEntry = true;

                    trigger OnValidate()
                    begin

                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;

                    trigger OnAssistEdit()
                    var
                        PackageNoInformation: Record "Package No. Information";
                        PackageNoInformationList: Page "Package No. Information List";
                    begin
                        if PackageNoInformationList.RunModal() = Action::LookupOK then begin
                            PackageNoInformationList.SetSelectionFilter(PackageNoInformation);
                            PackageNoInformation.FindSet(false);
                            repeat
                                Rec.Validate(Barcode, PackageNoInformation."Package No.");
                            until PackageNoInformation.Next() = 0;
                            CurrPage.Update();
                        end;
                    end;
                }
            }
            part(Lines; "Package Transfer Subpage SMK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
            }

        }

    }
    actions
    {
        area(Processing)
        {
            action(Post)
            {
                ApplicationArea = All;
                Caption = 'Post';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = Post;
                ToolTip = 'Executes the Post action.';
                PromotedOnly = true;

                trigger OnAction()
                begin
                    SumikaPackageTransMgt.OnAfterAction_Post_PagePackageTransferOrder(Rec);
                end;
            }
        }
    }
    trigger OnAfterGetRecord()
    begin
        Rec.CalcFields("Transferring to Prod. Location");
        TransferringToProdOrder := Rec."Transferring to Prod. Location";
    end;

    var
        SumikaPackageTransMgt: Codeunit "Sumika Package Trans. Mgt. SMK";
        TransferringToProdOrder: Boolean;
}