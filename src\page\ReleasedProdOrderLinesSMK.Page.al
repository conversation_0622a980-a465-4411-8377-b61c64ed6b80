page 60022 "Released Prod. Order Lines SMK"
{
    ApplicationArea = All;
    Caption = 'Released Prod. Order Lines';
    PageType = List;
    SourceTable = "Prod. Order Line";
    UsageCategory = Lists;
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field(Status; Rec.Status)
                {
                    ToolTip = 'Specifies a value that is copied from the corresponding field on the production order header.';
                }
                field("Prod. Order No."; Rec."Prod. Order No.")
                {
                    ToolTip = 'Specifies the number of the related production order.';
                }
                field("Line No."; Rec."Line No.")
                {
                    ToolTip = 'Specifies the value of the Line No. field.';
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the number of the item that is to be produced.';
                }
                field("Variant Code"; Rec."Variant Code")
                {
                    ToolTip = 'Specifies the variant of the item on the line.';
                }
                field(Description; Rec.Description)
                {
                    ToolTip = 'Specifies the value of the Description field on the item card. If you enter a variant code, the variant description is copied to this field instead.';
                }
                field("Description 2"; Rec."Description 2")
                {
                    ToolTip = 'Specifies an additional description.';
                }
                field(MachineName; SumikaProductionMgt.GetMachineNameFromProdOrderLine(Rec))
                {
                    Caption = 'Machine Name';
                    ToolTip = 'Specifies the value of the Machine Name field.';
                }
                field("Location Code";
                Rec."Location Code")
                {
                    ToolTip = 'Specifies the location code, if the produced items should be stored in a specific location.';
                }
                field("Shortcut Dimension 1 Code"; Rec."Shortcut Dimension 1 Code")
                {
                    ToolTip = 'Specifies the code for Shortcut Dimension 1, which is one of two global dimension codes that you set up in the General Ledger Setup window.';
                }
                field("Shortcut Dimension 2 Code"; Rec."Shortcut Dimension 2 Code")
                {
                    ToolTip = 'Specifies the code for Shortcut Dimension 2, which is one of two global dimension codes that you set up in the General Ledger Setup window.';
                }
                field("Bin Code"; Rec."Bin Code")
                {
                    ToolTip = 'Specifies the bin that the produced item is posted to as output, and from where it can be taken to storage or cross-docked.';
                }
                field(Quantity; Rec.Quantity)
                {
                    ToolTip = 'Specifies the quantity to be produced if you manually fill in this line.';
                }
                field("Finished Quantity"; Rec."Finished Quantity")
                {
                    ToolTip = 'Specifies how much of the quantity on this line has been produced.';
                }
                field("Remaining Quantity"; Rec."Remaining Quantity")
                {
                    ToolTip = 'Specifies the difference between the finished and planned quantities, or zero if the finished quantity is greater than the remaining quantity.';
                }
                field("Scrap %"; Rec."Scrap %")
                {
                    ToolTip = 'Specifies the percentage of the item that you expect to be scrapped in the production process.';
                }
                field("Due Date"; Rec."Due Date")
                {
                    ToolTip = 'Specifies the date when the produced item must be available. The date is copied from the header of the production order.';
                }
                field("Starting Date"; Rec."Starting Date")
                {
                    ToolTip = 'Specifies the value of the Starting Date field.';
                }
                field("Starting Time"; Rec."Starting Time")
                {
                    ToolTip = 'Specifies the value of the Starting Time field.';
                }
                field("Ending Date"; Rec."Ending Date")
                {
                    ToolTip = 'Specifies the value of the Ending Date field.';
                }
                field("Ending Time"; Rec."Ending Time")
                {
                    ToolTip = 'Specifies the value of the Ending Time field.';
                }
                field("Planning Level Code"; Rec."Planning Level Code")
                {
                    ToolTip = 'Specifies the value of the Planning Level Code field.';
                }
                field(Priority; Rec.Priority)
                {
                    ToolTip = 'Specifies the value of the Priority field.';
                }
                field("Production BOM No."; Rec."Production BOM No.")
                {
                    ToolTip = 'Specifies the number of the production BOM that is the basis for creating the Prod. Order Component list for this line.';
                }
                field("Routing No."; Rec."Routing No.")
                {
                    ToolTip = 'Specifies the number of the routing used as the basis for creating the production order routing for this line.';
                }
                field("Inventory Posting Group"; Rec."Inventory Posting Group")
                {
                    ToolTip = 'Specifies the value of the Inventory Posting Group field.';
                }
                field("Routing Reference No."; Rec."Routing Reference No.")
                {
                    ToolTip = 'Specifies the value of the Routing Reference No. field.';
                }
                field("Unit Cost"; Rec."Unit Cost")
                {
                    ToolTip = 'Specifies the cost of one unit of the item or resource on the line.';
                }
                field("Cost Amount"; Rec."Cost Amount")
                {
                    ToolTip = 'Specifies the total cost on the line by multiplying the unit cost by the quantity.';
                }
                field("Reserved Quantity"; Rec."Reserved Quantity")
                {
                    ToolTip = 'Specifies how many units of this item have been reserved.';
                }
                field("Qty. Rounding Precision"; Rec."Qty. Rounding Precision")
                {
                    ToolTip = 'Specifies the value of the Qty. Rounding Precision field.';
                }
                field("Qty. Rounding Precision (Base)"; Rec."Qty. Rounding Precision (Base)")
                {
                    ToolTip = 'Specifies the value of the Qty. Rounding Precision (Base) field.';
                }
                field("Unit of Measure Code"; Rec."Unit of Measure Code")
                {
                    ToolTip = 'Specifies how each unit of the item is measured, such as in pieces or tons.';
                }
                field("Quantity (Base)"; Rec."Quantity (Base)")
                {
                    ToolTip = 'Specifies the quantity for this entry, in base units of measure.';
                }
                field("Finished Qty. (Base)"; Rec."Finished Qty. (Base)")
                {
                    ToolTip = 'Specifies the value of the Finished Qty. (Base) field.';
                }
                field("Remaining Qty. (Base)"; Rec."Remaining Qty. (Base)")
                {
                    ToolTip = 'Specifies the value of the Remaining Qty. (Base) field.';
                }
                field("Reserved Qty. (Base)"; Rec."Reserved Qty. (Base)")
                {
                    ToolTip = 'Specifies the reserved quantity of the item, in base units of measure.';
                }
                field("Expected Operation Cost Amt."; Rec."Expected Operation Cost Amt.")
                {
                    ToolTip = 'Specifies the value of the Expected Operation Cost Amt. field.';
                }
                field("Total Exp. Oper. Output (Qty.)"; Rec."Total Exp. Oper. Output (Qty.)")
                {
                    ToolTip = 'Specifies the value of the Total Exp. Oper. Output (Qty.) field.';
                }
                field("Expected Component Cost Amt."; Rec."Expected Component Cost Amt.")
                {
                    ToolTip = 'Specifies the value of the Expected Component Cost Amt. field.';
                }
                field("Starting Date-Time"; Rec."Starting Date-Time")
                {
                    ToolTip = 'Specifies the starting date and the starting time, which are combined in a format called "starting date-time".';
                }
                field("Ending Date-Time"; Rec."Ending Date-Time")
                {
                    ToolTip = 'Specifies the ending date and the ending time, which are combined in a format called "ending date-time".';
                }
                field("Dimension Set ID"; Rec."Dimension Set ID")
                {
                    ToolTip = 'Specifies the value of the Dimension Set ID field.';
                }
                field("Cost Amount (ACY)"; Rec."Cost Amount (ACY)")
                {
                    ToolTip = 'Specifies the value of the Cost Amount (ACY) field.';
                }
                field("Unit Cost (ACY)"; Rec."Unit Cost (ACY)")
                {
                    ToolTip = 'Specifies the value of the Unit Cost (ACY) field.';
                }
                field("Lot No. SMK"; Rec."Lot No. SMK")
                {
                }
                field("Package Count SMK"; Rec."Package Count SMK")
                {
                }
                field("Production BOM Version Code"; Rec."Production BOM Version Code")
                {
                    ToolTip = 'Specifies the version code of the production BOM.';
                }
                field("Routing Version Code"; Rec."Routing Version Code")
                {
                    ToolTip = 'Specifies the version number of the routing.';
                }
                field("Routing Type"; Rec."Routing Type")
                {
                    ToolTip = 'Specifies the value of the Routing Type field.';
                }
                field("Qty. per Unit of Measure"; Rec."Qty. per Unit of Measure")
                {
                    ToolTip = 'Specifies the value of the Qty. per Unit of Measure field.';
                }
                field("MPS Order"; Rec."MPS Order")
                {
                    ToolTip = 'Specifies the value of the MPS Order field.';
                }
                field("Planning Flexibility"; Rec."Planning Flexibility")
                {
                    ToolTip = 'Specifies whether the supply represented by this line is considered by the planning system when calculating action messages.';
                }
                field("Indirect Cost %"; Rec."Indirect Cost %")
                {
                    ToolTip = 'Specifies the value of the Indirect Cost % field.';
                }
                field("Overhead Rate"; Rec."Overhead Rate")
                {
                    ToolTip = 'Specifies the value of the Overhead Rate field.';
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
                field(SystemCreatedBy; Rec.SystemCreatedBy)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedBy field.';
                }
                field(SystemId; Rec.SystemId)
                {
                    ToolTip = 'Specifies the value of the SystemId field.';
                }
                field(SystemModifiedAt; Rec.SystemModifiedAt)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedAt field.';
                }
                field(SystemModifiedBy; Rec.SystemModifiedBy)
                {
                    ToolTip = 'Specifies the value of the SystemModifiedBy field.';
                }
            }
        }
    }
    var
        SumikaProductionMgt: Codeunit "Sumika Production Mgt. SMK";
}