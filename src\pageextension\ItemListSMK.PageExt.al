pageextension 60015 "Item List SMK" extends "Item List"
{
    layout
    {
        addafter("No.")
        {
            field("No. 2 SMK"; Rec."No. 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the No. 2 field.';
            }
        }
        addafter(Description)
        {
            field("Description 2 SMK"; Rec."Description 2")
            {
                ApplicationArea = All;
                ToolTip = 'Specifies the value of the Description 2 field.';
            }
            field("Packaging Type SMK"; Rec."Packaging Type SMK")
            {
                ApplicationArea = All;
            }

        }
    }
}