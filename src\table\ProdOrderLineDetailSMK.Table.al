table 60015 "Prod. Order Line Detail SMK"
{
    Caption = 'Prod. Order Line Detail';
    DataClassification = CustomerContent;
    LookupPageId = "Prod. Order Line Details SMK";
    DrillDownPageId = "Prod. Order Line Details SMK";

    fields
    {
        field(1; Status; Enum "Production Order Status")
        {
            Caption = 'Status';
            AllowInCustomizations = Always;
        }
        field(2; "Production Order No."; Code[20])
        {
            Caption = 'Production Order No.';
            AllowInCustomizations = Always;
        }
        field(3; "Production Order Line No."; Integer)
        {
            Caption = 'Production Order Line No.';
            AllowInCustomizations = Always;
        }
        field(4; "Line No."; Integer)
        {
            Caption = 'Line No.';
            AllowInCustomizations = Always;
        }
        field(5; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            ToolTip = 'Specifies the value of the Item No. field.';
        }
        field(6; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            TableRelation = "Item Variant".Code where("Item No." = field("Item No."));
            ToolTip = 'Specifies the value of the Variant Code field.';
        }
        field(7; "Item Description"; Text[100])
        {
            Caption = 'Item Description';
            ToolTip = 'Specifies the value of the Item Description field.';
        }
        field(8; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            ToolTip = 'Specifies the value of the Location Code field.';
            TableRelation = Location.Code;
        }
        field(9; "Bin Code"; Code[20])
        {
            Caption = 'Bin Code';
            ToolTip = 'Specifies the value of the Bin Code field.';
            TableRelation = Bin.Code where("Location Code" = field("Location Code"));
        }
        field(10; Quantity; Decimal)
        {
            Caption = 'Quantity';
            ToolTip = 'Specifies the value of the Quantity field.';
        }
        field(11; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(12; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            ToolTip = 'Specifies the value of the Package No. field.';
        }
        field(13; Posted; Boolean)
        {
            Caption = 'Posted';
            ToolTip = 'Specifies the value of the Posted field.';
        }
        field(14; "Parent Package No."; Code[50])
        {
            Caption = 'Parent Package No.';
            ToolTip = 'Specifies the value of the Parent Package No. field.';
        }
        field(15; "Child Package Count"; Integer)
        {
            Caption = 'Child Package Count';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = count("Package No. Information" where("Parent Package No. SMK" = field("Package No.")));
            ToolTip = 'Specifies the value of the Child Package Count field.';
        }
        field(16; "Automatic Consumption"; Boolean)
        {
            Caption = 'Automatic Consumption';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Automatic Consumption SMK" where("No." = field("Item No.")));
            AllowInCustomizations = Always;
        }
        field(17; "Package Order No."; Integer)
        {
            Caption = 'Package Order No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Package Order No. SMK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the value of the Package Order No. field.';
        }
        field(18; "Quality Control Status"; Enum "Quality Control Status SMK")
        {
            Caption = 'Quality Control Status';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Quality Control Status SMK" where("Package No." = field("Package No.")));
            ToolTip = 'Specifies the quality control status of the package.';
        }

        // field(17; "Label to Print Count"; Integer)
        // {
        //     Caption = 'Label to Print Count';
        //     ToolTip = 'Specifies the value of the Label to Print Quantity field.';
        // }
    }
    keys
    {
        key(PK; Status, "Production Order No.", "Production Order Line No.", "Line No.")
        {
            Clustered = true;
        }
        key(SK; SystemCreatedAt)
        { }
    }
    trigger OnInsert()
    var
        ProdOrderLineDetail: Record "Prod. Order Line Detail SMK";
    begin
        ProdOrderLineDetail.SetRange(Status, Rec.Status);
        ProdOrderLineDetail.SetRange("Production Order No.", Rec."Production Order No.");
        ProdOrderLineDetail.SetRange("Production Order Line No.", Rec."Production Order Line No.");
        if ProdOrderLineDetail.FindLast() then
            Rec."Line No." := ProdOrderLineDetail."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}