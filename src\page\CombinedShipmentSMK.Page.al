page 60026 "Combined Shipment SMK"
{
    ApplicationArea = All;
    Caption = 'Combined Shipment';
    PageType = Document;
    SourceTable = "Combined Shipment Header SMK";
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            usercontrol(SetFieldFocus; "SetFieldFocus SMK")
            {
                trigger Ready()
                begin
                    CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                end;
            }
            group(General)
            {
                Caption = 'General';
                Editable = not Rec."Transferred to Sales Order";
                field("No."; Rec."No.")
                {
                }
                field("Customer No."; Rec."Customer No.")
                {
                }
                field("Customer Name"; Rec."Customer Name")
                {
                }
                field("Sales Order No."; Rec."Sales Order No.")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                // field("Shipment Location Code"; Rec."Shipment Location Code")
                // {
                //     ToolTip = 'Specifies the value of the Shipment Location Code field.';
                //     Editable = ShipmentLocationAndBinEditable;
                // }
                // field("Shipment Bin Code"; Rec."Shipment Bin Code")
                // {
                //     ToolTip = 'Specifies the value of the Shipment Bin Code field.';
                //     Editable = ShipmentLocationAndBinEditable;
                // }
                field("Total Package Quantity"; Rec."Total Package Count")
                {
                }
                field(Shipped; Rec."Transferred to Sales Order")
                {
                }
                field("Posted Shipment No."; Rec."Posted Shipment No.")
                {
                    DrillDown = true;

                    trigger OnDrillDown()
                    var
                        SalesShipmentHeader: Record "Sales Shipment Header";
                        PostedSalesShipment: Page "Posted Sales Shipment";
                    begin
                        if Rec."Posted Shipment No." = '' then
                            exit;

                        SalesShipmentHeader.Get(Rec."Posted Shipment No.");
                        PostedSalesShipment.SetRecord(SalesShipmentHeader);
                        PostedSalesShipment.Run();
                    end;
                }
                field(SystemCreatedAt; Rec.SystemCreatedAt)
                {
                    ToolTip = 'Specifies the value of the SystemCreatedAt field.';
                }
            }
            group(BarcodeReadingArea)
            {
                Caption = 'Barcode Reading';
                Editable = not Rec."Transferred to Sales Order";
                field(Barcode; Rec.Barcode)
                {
                    trigger OnValidate()
                    begin
                        CurrPage.Update();
                        CurrPage.SetFieldFocus.SetFocusOnField('Barcode');
                    end;

                    trigger OnAssistEdit()
                    var
                        PackageNoInformation: Record "Package No. Information";
                        PackageNoInformationList: Page "Package No. Information List";
                    begin
                        if PackageNoInformationList.RunModal() = Action::LookupOK then begin
                            PackageNoInformationList.SetSelectionFilter(PackageNoInformation);
                            PackageNoInformation.FindSet(false);
                            repeat
                                Rec.Validate(Barcode, PackageNoInformation."Package No.");
                            //SumikaSalesManagement.ProcessLabel(Rec);
                            until PackageNoInformation.Next() = 0;
                            CurrPage.Update();
                        end;
                    end;
                }
            }
            part(Lines; "Combined Shipment Lines SMK")
            {
                Caption = 'Lines';
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = not Rec."Transferred to Sales Order";
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(GetOrderLines)
            {
                ApplicationArea = All;
                Caption = 'Get Order Lines';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = GetLines;
                PromotedOnly = true;
                ToolTip = 'Executes the Get Order Lines action.';

                trigger OnAction()
                begin
                    SumikaSalesManagement.GetSalesOrderLines(Rec);
                end;
            }
            action(PrepareForShipment)
            {
                ApplicationArea = All;
                Caption = 'Prepare For Shipment';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = TransferOrder;
                PromotedOnly = true;
                ToolTip = 'Executes the Prepare For Shipment action.';

                trigger OnAction()
                begin
                    SumikaSalesManagement.PrepareForShipment(Rec);
                end;
            }
            action(TransferReadPackagesToSalesOrder)
            {
                ApplicationArea = All;
                Caption = 'Transfer Read Packages To Sales Order';
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                Image = ReleaseShipment;
                PromotedOnly = true;
                ToolTip = 'Executes the Transfer Read Packages To Sales Order action.';

                trigger OnAction()
                begin
                    SumikaSalesManagement.TransferReadPackagesToSalesOrder(Rec);
                end;
            }
        }
    }
    // trigger OnOpenPage()
    // begin
    //     ShipmentLocationAndBinEditable := true;

    //     Rec.CalcFields("Total Package Quantity");
    //     if Rec."Total Package Quantity" <> 0 then
    //         ShipmentLocationAndBinEditable := false;
    // end;

    // trigger OnAfterGetCurrRecord()
    // begin
    //     Rec.CalcFields("Total Package Quantity");
    //     if Rec."Total Package Quantity" <> 0 then
    //         ShipmentLocationAndBinEditable := false;
    // end;

    var
        SumikaSalesManagement: Codeunit "Sumika Sales Management SMK";
    //ShipmentLocationAndBinEditable: Boolean;
}