tableextension 60018 "Sales Shipment Line SMK" extends "Sales Shipment Line"
{
    fields
    {
        field(60001; "Bill-to Customer Name SMK"; Text[100])
        {
            Caption = 'Bill-to Customer Name';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Shipment Header"."Bill-to Name" where("No." = field("Document No.")));
            ToolTip = 'Specifies the value of the Bill-to Customer Name field.';
        }
        field(60002; "Packaging Type SMK"; Code[100])
        {
            Caption = 'Packaging Type';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Packaging Type SMK" where("No." = field("No.")));
            ToolTip = 'Specifies the packaging type of the item.';
        }
    }
}