tableextension 60029 "Item Attribute Value Mapping" extends "Item Attribute Value Mapping"
{
    fields
    {
        modify("No.")
        {
            TableRelation = if ("Table ID" = const(27)) Item."No.";
        }
        field(60000; "Description SMK"; Text[100])
        {
            Caption = 'Description';
            ToolTip = 'Specifies the description of the record to which the attribute applies.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item.Description where("No." = field("No.")));
        }
        field(60001; "Item Attiribute Name SMK"; Text[250])
        {
            Caption = 'Item Attiribute Name';
            ToolTip = 'Specifies the name of the item attribute.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Attribute".Name where(ID = field("Item Attribute ID")));
        }
        field(60002; "Item Attribute Value Name SMK"; Text[250])
        {
            Caption = 'Item Attribute Value Name';
            ToolTip = 'Specifies the name of the item attribute value.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Attribute Value".Value where("Attribute ID" = field("Item Attribute ID"), ID = field("Item Attribute Value ID")));
        }
    }
}