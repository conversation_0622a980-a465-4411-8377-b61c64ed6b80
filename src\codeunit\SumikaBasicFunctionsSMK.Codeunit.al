codeunit 60014 "Sumika Basic Functions SMK"
{
    procedure GetLCYCode(): Code[10]
    var
        GeneralLedgerSetup: Record "General Ledger Setup";
    begin
        GeneralLedgerSetup.GetRecordOnce();

        exit(GeneralLedgerSetup."LCY Code");
    end;

    procedure GetPackagingTypeCodeFromItemNo(ItemNo: Code[20]): Code[100]
    var
        Item: Record Item;
    begin
        if Item.Get(ItemNo) then
            exit(Item."Packaging Type SMK");
    end;

    procedure CalculateTransferredQtyFromItemJournalLine(ItemJournalLine: Record "Item Journal Line") TransferredQty: Decimal
    var
        PackageNoInformation: Record "Package No. Information";
    begin
        PackageNoInformation.SetAutoCalcFields(Inventory);
        PackageNoInformation.SetRange("Item No.", ItemJournalLine."Item No.");
        PackageNoInformation.SetRange("Production Order No. SMK", ItemJournalLine."Order No.");
        PackageNoInformation.SetRange("Variant Code", ItemJournalLine."Variant Code");
        if not PackageNoInformation.FindSet(false) then
            exit(0);

        repeat
            TransferredQty += PackageNoInformation.Inventory;
        until PackageNoInformation.Next() = 0;

        exit(TransferredQty);
    end;

    procedure GetExpirationDateFromLotNo(LotNo: Code[50]): Date
    var
        ItemLedgerEntry: Record "Item Ledger Entry";
    begin
        ItemLedgerEntry.SetRange("Lot No.", LotNo);
        if ItemLedgerEntry.FindLast() then
            exit(ItemLedgerEntry."Expiration Date");

        exit(0D);
    end;

    procedure GetUserNameFromSecurityId(UserSecurityID: Guid): Code[50]
    var
        User: Record User;
    begin
        if User.Get(UserSecurityID) then
            exit(User."User Name");
        exit('');
    end;

    procedure GetFullUserNameFromUserId(UserId: Code[50]): Text[80]
    var
        User: Record User;
    begin
        User.SetRange("User Name", UserId);
        if User.FindFirst() then
            exit(User."Full Name");
        exit('');
    end;
}