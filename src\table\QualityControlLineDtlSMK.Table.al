table 60012 "Quality Control Line Dtl. SMK"
{
    Caption = 'Quality Control Line Detail';
    DataClassification = CustomerContent;
    DrillDownPageId = "Quality Ctrl. Line Dtls. SMK";
    LookupPageId = "Quality Ctrl. Line Dtls. SMK";

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            ToolTip = 'Specifies the value of the Document No. field.';
        }
        field(2; "Document Line No."; Integer)
        {
            Caption = 'Document Line No.';
            ToolTip = 'Specifies the value of the Document Line No. field.';
        }
        field(3; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the value of the Line No. field.';
        }
        field(4; "Specification Code"; Code[10])
        {
            Caption = 'Specification Code';
            ToolTip = 'Specifies the value of the Specification Code field.';
        }
        field(5; "Specification Description"; Text[100])
        {
            Caption = 'Specification Description';
            ToolTip = 'Specifies the value of the Specification Description field.';
        }
        field(6; "Specification Reference"; Enum "Q.C. Spec. Reference Type SMK")
        {
            Caption = 'Specification Reference';
            ToolTip = 'Specifies the value of the Specification Reference field.';
        }
        field(7; "Min Value"; Decimal)
        {
            Caption = 'Min Value';
            ToolTip = 'Specifies the value of the Min Value field.';
        }
        field(8; "Max Value"; Decimal)
        {
            Caption = 'Max Value';
            ToolTip = 'Specifies the value of the Max Value field.';
        }
        field(9; "Exact Value"; Decimal)
        {
            Caption = 'Exact Value';
            ToolTip = 'Specifies the value of the Exact Value field.';
        }
        field(10; "Selection Value"; Enum "Quality Control Selection SMK")
        {
            Caption = 'Selection Value';
            ToolTip = 'Specifies the value of the Selection Value field.';
        }
        field(11; "Unit of Measure Code"; Code[10])
        {
            Caption = 'Unit of Measure Code';
            Editable = false;
            ToolTip = 'Specifies the value of the Unit of Measure Code field.';
        }
        field(12; Standard; Text[30])
        {
            Caption = 'Standard';
            Editable = false;
            ToolTip = 'Specifies the value of the Standard field.';
        }
        field(13; "Result Value"; Decimal)
        {
            Caption = 'Result Value';
            ToolTip = 'Specifies the value of the Result Value field.';
            trigger OnValidate()
            begin
                Rec.TestField("Package No.");
            end;
        }
        field(14; "Package No."; Code[50])
        {
            Caption = 'Package No.';
            TableRelation = "Package No. Information"."Package No." where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Lot No. SMK" = field("Lot No."));
            ToolTip = 'Specifies the value of the Package No. field.';
            trigger OnValidate()
            begin
                Rec.TestField("Result Value", 0);
            end;
        }
        field(15; "Item No."; Code[20])
        {
            Caption = 'Item No.';
            AllowInCustomizations = Always;
        }
        field(16; "Variant Code"; Code[10])
        {
            Caption = 'Variant Code';
            AllowInCustomizations = Always;
        }
        field(17; "Lot No."; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
            AllowInCustomizations = Always;
        }
        field(18; "Created By"; Code[50])
        {
            Caption = 'Created By';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(User."User Name" where("User Security ID" = field(SystemCreatedBy)));
            ToolTip = 'Specifies the value of the Created By field.';
        }
        field(19; "Line Status"; Enum "Quality Control Status SMK")
        {
            Caption = 'Line Status';
            InitValue = "Input Pending";
            ToolTip = 'Specifies the value of the Line Status field.';
        }
        field(20; "QC Control Quantity"; Decimal)
        {
            Caption = 'Q.C. Control Quantity';
            ToolTip = 'Specifies the value of the Q.C. Control Quantity field.';
        }
        field(21; "Selection Result Value"; Enum "Quality Control Selection SMK")
        {
            Caption = 'Selection Result Value';
            ToolTip = 'Specifies the value of the Selection Result Value field.';
            trigger OnValidate()
            begin
                //SumikaQualityCtrlMgt.CalculateQualityControlLineStatus(Rec);
            end;
        }
        field(22; "Source Q.C. Document No."; Code[20])
        {
            Caption = 'Source Q.C. Document No.';
            TableRelation = "Quality Control Header SMK"."No.";
            ToolTip = 'Specifies the value of the Source Q.C. Document No. field.';
        }
        field(23; "Package Order No."; Integer)
        {
            Caption = 'Package Order No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information"."Package Order No. SMK" where("Package No." = field("Package No."), "Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Lot No. SMK" = field("Lot No.")));
            ToolTip = 'Specifies the Package Order No. associated with this quality control line.';
        }
        field(24; "Package Comment Count"; Integer)
        {
            Caption = 'Package Comments';
            Editable = false;
            FieldClass = FlowField;
            //CalcFormula = count("Package Comment SMK" where("Package No." = field("Package No.")));
            CalcFormula = count("Item Tracking Comment" where("Item No." = field("Item No."), "Variant Code" = field("Variant Code"), "Serial/Lot No." = field("Package No.")));
            ToolTip = 'Specifies the number of comments for the selected package.';
        }
        field(25; Description; Text[100])
        {
            Caption = 'Description';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Package No. Information".Description where("Item No." = field("Item No."),
                                                                             "Variant Code" = field("Variant Code"),
                                                                             "Package No." = field("Package No.")));
            ToolTip = 'Specifies the description of the package from Package No. Information.';
        }
    }
    keys
    {
        key(PK; "Document No.", "Document Line No.", "Line No.")
        {
            Clustered = true;
        }
    }
    trigger OnInsert()
    var
        QualityControlLineDtl: Record "Quality Control Line Dtl. SMK";
    begin
        QualityControlLineDtl.SetRange("Document No.", Rec."Document No.");
        QualityControlLineDtl.SetRange("Document Line No.", Rec."Document Line No.");
        if QualityControlLineDtl.FindLast() then
            Rec."Line No." := QualityControlLineDtl."Line No." + 10000
        else
            Rec."Line No." := 10000;
    end;
}